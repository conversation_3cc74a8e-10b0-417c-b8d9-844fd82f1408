ig.module('game.entities.buttons.button-control-fire')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonControlFire = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/button-control-fire.png', 70, 70),
        size: { x: 90, y: 90 },
        offset: { x: -10, y: -10 },
		zIndex: 205,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
        click: false,
        hold: false,
		isTutorial: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
            if (!this.isTutorial) {
				this.setScale(1.5, 1.5);
			} else {
				this.setScale(0.4, 0.4);
			}
			this.gAlpha = settings.gAlpha;
			if (!ig.global.wm) {
				ig.game.btnCtrlFire = this;
			}
		},
		clicked: function () {
			if (!this.isTutorial) {
				this.setScale(1.4, 1.4);
				this.click = true;
			}
		},
		clicking: function () {
			if (!this.isTutorial) {
				this.setScale(1.4, 1.4);
				// this.hold = true;
			}
		},
		released: function () {
			if (!this.isTutorial) {
				this.setScale(1.5, 1.5);
				this.click = false;
				// this.hold = false;
			}
		},
		over: function () {
		},
		leave: function () {
        },
		draw: function () {
			if (!this.isTutorial) {
				if (this.click) { // || this.hold
					this.currentAnim.alpha = 1;
				} else {
					this.currentAnim.alpha = 0.7;
				}
				if (!ig.game.gameControl.isPaused) {
					this.parent();
				}
			} else {
				this.parent();
			}
		},
		update: function () {
			this.parent();
			if (!this.isTutorial) {
				if (this.click) { //  || this.hold
					var clickingCheck = false;

					for (var t in ig.input.touches) {
						if (ig.input.touches[t].x > this.pos.x &&
							ig.input.touches[t].x < (this.pos.x + this.size.x) &&
							ig.input.touches[t].y > this.pos.y &&
							ig.input.touches[t].y < (this.pos.y + this.size.y)) {
							clickingCheck = true;
							break;
						}
					}

					if (clickingCheck == false) {
						this.released();
					}
				}
			}
		}
	});
});
