ig.module('game.entities.explosion')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityExplosion = ig.Entity.extend({
        type: ig.Entity.TYPE.NONE,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/explosion.png', 70, 70),
		itemKey: 0,
		zIndex: 200,
		size: { x: 70, y: 70 },
		gAlpha: 0,
		targetAlpha: 1,
        alphaTime: 1,
        explosionScale: { x: 0.5, y: 0.5 },

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('explode', 0.15, [0, 1, 2]);
            this.setScale(this.explosionScale.x, this.explosionScale.y);
            this.currentAnim = this.anims.explode;
		},

		draw: function () {
			this.context = ig.system.context;
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
                // this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
                this.context.globalAlpha = this.targetAlpha;
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
        },

        update: function () {
            this.parent();
            if (this.currentAnim.loopCount > 0) {
                this.kill();
            }
        },

        check: function (other) {
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
    })
})
