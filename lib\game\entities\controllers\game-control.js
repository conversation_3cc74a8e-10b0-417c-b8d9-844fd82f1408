ig.module('game.entities.controllers.game-control')
    .requires(
        'impact.entity'
    )
    .defines(function () {
        EntityGameControl = ig.Entity.extend({
            zIndex: 10000,
            size: new Vector2(0, 0),
            tween: null,
            resultDelay: null,
            playerDeathDelay: null,
            collideOnce: false,
            onEdge: false,
            edgeTimer: new ig.Timer(),
            flipDirection: false,
            isPaused: false,
            stepCount: 0,
            isHolding: false,
            checkResult: false,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.global.wm) {
                    // alias
                    ig.game.gameControl = this;
                }

                // We transfer the playerHealth to another variable to save the initial health
                // We limit the player health to 5 to avoid overlapping UIs
                if (ig.game.playerHealth > 5) {
                    ig.game.playerHealth = 5;
                }
                this.playerHealth = ig.game.playerHealth;
                this.resultDelay = new ig.Timer();
                this.playerDeathDelay = new ig.Timer();
                this.enablePlayerFiring = true;
                ig.game.sessionData.attempts++;
                ig.game.submitStats();
            },

            ready: function () {
                this.parent();
                this.initGame();
            },

            initGame: function () {
                if (typeof (_INIT.config.timeLimit) == "number" && _INIT.config.timeLimit > 0) {
                    this.timelimitTimer = new ig.Timer();
                }
                ig.game.gameControl = this;
                this.gameTimer = new ig.Timer();
                this.checkResult = false;
                this.spawnGameButtons();
                this.drawTexts();
                this.spawnLife();
                this.spawnPlayer();
                this.spawnShelters();
                this.spawnCollisions();
                this.updatePlayerLife();
                this.gameLevelControl = ig.game.spawnEntity(EntityGameLevelControl, 0, 0);
                this.initGameInitialized = true;
                ig.game.isGameReady = true;
                
                this.highScore = ig.game.load('highScore');
            },

            spawnCollisions: function () {
                // Custom Collisions
                this.screenTop = ig.game.spawnEntity(EntityScreenTop, 0, 0);
                this.screenLeft = ig.game.spawnEntity(EntityScreenSide, 0, 0);
                this.screenRight = ig.game.spawnEntity(EntityScreenSide, ig.system.width, 0);
                this.screenBottom = ig.game.spawnEntity(EntityScreenBottom, 0, ig.system.height - 15);
                this.screenBase = ig.game.spawnEntity(EntityScreenBase, 0, Math.floor(ig.system.height * 0.78));
            },

            spawnGameButtons: function () {
                // In-Game buttons
                this.gameButtons = {
                    audio: ig.game.spawnEntity(EntityButtonSound, ig.system.width - 135, 10, {
                        control: this,
                        size: new Vector2(60, 70),
                        buttonScale: {
                            x: 0.7,
                            y: 0.7
                        },
                        offset: {
                            x: -8,
                            y: -5
                        }
                    }),
                    help: ig.game.spawnEntity(EntityButtonTutorialGame, ig.system.width - 75, 8, {
                        control: this,
                        size: new Vector2(60, 70),
                        buttonScale: {
                            x: 0.7,
                            y: 0.7
                        },
                        offset: {
                            x: -10,
                            y: -5
                        }
                    })
                };

                if (ig.ua.mobile && ig.ua.iOS) {
                    this.gameButtons.help.pos = {
                        x: ig.system.width - this.gameButtons.help.size.x * 1.5,
                        y: this.gameButtons.help.size.y * 1.25
                    }
                    this.gameButtons.audio.pos = {
                        x: this.gameButtons.help.pos.x - 4,
                        y: this.gameButtons.help.pos.y + this.gameButtons.audio.size.y * 1.1
                    }
                }

                // Mobile Buttons
                if (ig.ua.mobile) {
                    this.buttonControlLeft = ig.game.spawnEntity(EntityButtonControlLeft,
                        ig.system.width * 0.5 / 8,
                        ig.system.height * 6 / 8
                    );
                    this.buttonControlRight = ig.game.spawnEntity(EntityButtonControlRight,
                        ig.system.width * 1.5 / 8,
                        ig.system.height * 6 / 8
                    );
                    this.buttonControlFire = ig.game.spawnEntity(EntityButtonControlFire,
                        ig.system.width * 6.7 / 8,
                        ig.system.height * 5.8 / 8
                    );
                }
            },

            drawTexts: function () {
                var scoreText = _STRINGS.Game.Score;
                var lifeText = _STRINGS.Game.Life;
                var ctx = ig.system.context;
                ctx.font = '100 30px eight-bit-madness';
                ctx.fillStyle = '#989afe';
                ctx.fillText(scoreText, ig.system.width * 0.45, 40);
                ctx.fillText(lifeText, ig.system.width * 0.06, 40);
            },

            drawScore: function () {
                var ctx = ig.system.context;
                ctx.font = '100 30px eight-bit-madness';
                ctx.fillStyle = '#989afe';
                ctx.fillText(ig.game.score, ig.system.width * 0.5 + 50, 40);
            },

            spawnLife: function () {
                var maxHealth = ig.game.playerHealth;
                this.lives = {};
                for (var i = 1; i <= maxHealth; i++) {
                    // we set y-axis to be negative 4 to offset the scaling done, as we know, scaling the entity changes the position
                    this.lives['life' + i] = ig.game.spawnEntity(EntityLife, (ig.system.width * 0.10) + 30 * i, -4);
                }
            },

            spawnPlayer: function (playerPositionX, playerPositionY) {
                ig.game.isPlayerAlive = true;
                if (!playerPositionX && !playerPositionY) {
                    playerPositionX = ig.system.width * 0.5 - 30;
                    playerPositionY = ig.system.height * 0.82;
                }
                this.player = ig.game.spawnEntity(EntityPlayer, playerPositionX, playerPositionY);
                this.player.setScale(0.85, 0.85);
            },

            spawnShelters: function () {
                // we can choose between two logics. will leave the original commented

                // this.shelters = {
                //     shelter1: ig.game.spawnEntity(EntityBaseShelter, Math.floor((ig.system.width / 5 - 35)), Math.floor(ig.system.height * 0.68)),
                //     shelter2: ig.game.spawnEntity(EntityBaseShelter, Math.floor((ig.system.width / 5 - 35)) + 192, Math.floor(ig.system.height * 0.68)),
                //     shelter3: ig.game.spawnEntity(EntityBaseShelter, Math.floor((ig.system.width / 5 - 35)) + 384, Math.floor(ig.system.height * 0.68)),
                //     shelter4: ig.game.spawnEntity(EntityBaseShelter, Math.floor((ig.system.width / 5 - 35)) + 576, Math.floor(ig.system.height * 0.68))
                // };
                this.shelters = {};
                // Number 4 here is the shelter count
                for (var i = 1; i <= 4; i++) {
                    this.shelters['shelter' + i] = ig.game.spawnEntity(EntityBaseShelter, 0, 0);
                    this.shelters['shelter' + i].pos = {
                        x: ig.system.width / 4 * i - (this.shelters['shelter' + i].size.x * 2.208), // 2.208 is a result of trial and error to position the shelters
                        y: ig.system.height * 0.68
                    };
                };
            },

            killGameButtons: function () {
                // In-Game buttons
                this.gameButtons.audio.kill();
                this.gameButtons.help.kill();

                // Mobile Buttons
                if (ig.ua.mobile) {
                    this.buttonControlLeft.kill();
                    this.buttonControlRight.kill();
                    this.buttonControlFire.kill();
                }
            },

            updatePlayerLife: function (state) {
                // we can choose between two logics. will leave the original commented. New logic is a more dynamic approach

                // if (this.playerHealth == 3) {
                //     this.lives.life1.currentAnim = this.lives.life3.anims.fill;
                //     this.lives.life2.currentAnim = this.lives.life3.anims.fill;
                //     this.lives.life3.currentAnim = this.lives.life3.anims.fill;
                // } else if (this.playerHealth == 2) {
                //     this.lives.life1.currentAnim = this.lives.life3.anims.fill;
                //     this.lives.life2.currentAnim = this.lives.life3.anims.fill;
                //     this.lives.life3.currentAnim = this.lives.life3.anims.empty;
                // } else if (this.playerHealth == 1) {
                //     this.lives.life1.currentAnim = this.lives.life3.anims.fill;
                //     this.lives.life2.currentAnim = this.lives.life2.anims.empty;
                //     this.lives.life3.currentAnim = this.lives.life3.anims.empty;
                // } else if (this.playerHealth == 0) {
                //     ig.game.isPlayerAlive = false;
                //     this.lives.life1.currentAnim = this.lives.life1.anims.empty;
                //     this.lives.life2.currentAnim = this.lives.life2.anims.empty;
                //     this.lives.life3.currentAnim = this.lives.life3.anims.empty;
                // }
                switch (state) {
                    case 'increase':
                        this.lives['life' + this.playerHealth].currentAnim = this.lives['life' + this.playerHealth].anims.fill;
                        break;
                    case 'decrease':
                        this.lives['life' + (this.playerHealth + 1)].currentAnim = this.lives['life' + (this.playerHealth + 1)].anims.empty;
                        break;
                    default:
                        for (var c = 1; c <= this.playerHealth; c++) {
                            this.lives['life' + c].currentAnim = this.lives['life' + c].anims.fill;
                        };
                }
            },

            handleResult: function () {
                if (this.resultDelay.delta() > 2 && !ig.game.isPlayerAlive) {
                    this.resultDelay.set(2);
                } else if (this.resultDelay.delta() > 0 && this.resultDelay.delta() < 3) {
                    this.resetTimers();
                    if (ig.game.ufo && ig.game.ufo.isUfoAlive) {
                        ig.game.ufo.kill();
                    }
                    ig.game.director.jumpTo(LevelGameResult);
                }
            },
            handleResult2: function () {
                this.resetTimers();
                if (ig.game.ufo && ig.game.ufo.isUfoAlive) {
                    ig.game.ufo.kill();
                }
                ig.game.director.jumpTo(LevelGameResult);
            },

            handleEdgeTimer: function () {
                if (ig.game.gameLevelControl.enableGridBasedMovement) {
                    if (this.edgeTimer.delta() > 0 && this.edgeTimer.delta() < 2) {
                        ig.game.gameLevelControl.gridForceStopMoving = false;
                    } else if (this.edgeTimer.delta() > 2 && this.collideOnce) {
                        this.collideOnce = false;
                        // this.stepCount++;
                        // console.log(this.stepCount);
                    }
                } else {
                    if ((this.edgeTimer.delta() > 0 && this.edgeTimer.delta() < 3) && this.collideOnce) {
                        this.onEdge = false;
                    } else if (this.edgeTimer.delta() > 3 && this.collideOnce) {
                        this.collideOnce = false;
                        // this.stepCount++;
                        // console.log(this.stepCount);
                    }
                }
            },

            resetTimers: function () {
                this.resultDelay.reset();
                this.playerDeathDelay.reset();
                this.gameLevelControl.ufoTimer.reset();
                this.gameLevelControl.enemyProjectileCooldown.reset();
            },

            update: function () {
                this.parent();

                if (typeof (_INIT.config.timeLimit) == "number" && _INIT.config.timeLimit > 0) {
                    if (this.timelimitTimer.delta() >= _INIT.config.timeLimit && !this.checkResult) {
                        this.checkResult = true;
                        this.handleResult2();
                    }
                }

                if (typeof (_INIT.config.scoreLimit) == "number" && _INIT.config.scoreLimit > 0) {
                    if (ig.game.score >= _INIT.config.scoreLimit) {
                        this.handleResult2();
                    }
                }

                // this.updatePlayerLife();
                this.handleEdgeTimer();
                // handling result is here in order for the delay before the game over screen to work
                if (this.playerHealth == 0 || ig.game.gameLevelControl.hasEnemyReachedBase) {
                    this.handleResult();
                }
                // Force a Damage, or add a score. For debugging purposes
                // if (ig.input.pressed('rightClick')) {
                //     // ig.game.score += 100;
                //     ig.game.player.damageCount = 1;
                //     ig.game.player.isDamaged = true;
                //     ig.game.player.damagePlayer();
                // }
            },

            draw: function () {
                this.parent();
                if (!this.isPaused) {
                    this.drawScore();
                    this.drawTexts();
                }
            }
        });
    });