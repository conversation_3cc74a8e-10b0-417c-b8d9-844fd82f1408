ig.module('game.entities.buttons.button-back')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonBack = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/back-button.png', 39, 41),
		size: { x: 50, y: 50 },
		zIndex: 750,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
		},
		clicked: function () {
			if (!ig.game.isGame) { // Not in game
				ig.game.director.previousLevel();
			} else { // In game
				ig.game.gameControl.isPaused = false;
				ig.game.tutorialControl.removeElements();
				ig.game.gameControl.edgeTimer.unpause();
				ig.game.gameControl.playerDeathDelay.unpause();
				ig.game.gameLevelControl.ufoTimer.unpause();
				ig.game.gameLevelControl.levelTimer.unpause();
				if (ig.game.ufo) { // Check if UFO is spawned
					ig.soundHandler.sfxPlayer.unmute(ig.game.ufo.ufoSound);
				}
				ig.game.gameLevelControl.enemyProjectileCooldown.unpause();
				ig.game.gameControl.spawnGameButtons();
			}
		},
		clicking: function () {

		},
		released: function () {
			this.setScale(1, 1);
		},

		over: function () {
			this.setScale(1.1, 1.1);
		},
		leave: function () {
			this.setScale(1, 1);
		}
	})
})
