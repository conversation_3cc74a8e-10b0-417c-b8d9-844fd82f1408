ig.module('game.entities.buttons.button-blank')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonBlank = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/blank-button.png', 248, 54),
		size: { x: 248, y: 54 },
		zIndex: 205,
		gAlpha: 0,
		targetAlpha: 1,
        isDisabled: false,
        text: '',
        fontSize:'40px',
        isScale: true,
		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
            this.gAlpha = settings.gAlpha;
		},
		clicked: function () {
            this.setScale(1.1, 1.1);
            if(this.isScale) this.fontSize = '44px';
            else this.fontSize = '31px';
		},
		clicking: function () {

		},
		released: function () {
            this.setScale(1, 1);
            if(this.isScale) this.fontSize = '40px';
            else this.fontSize = '31px';
		},

		over: function () {
		},
		leave: function () {
            this.setScale(1, 1);
            if(this.isScale) this.fontSize = '40px';
            else this.fontSize = '27px';
        },

        draw: function () {
            this.parent();

            var ctx = ig.system.context;
            ctx.save();

            ctx.font = this.fontSize + ' eight-bit-madness';
            ctx.fillStyle = '#4cd82b';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            var finalText = _FITSTRING(this.text, this.size.x);
            ctx.fillText(finalText, this.pos.x + (this.size.x / 2), this.pos.y + (this.size.y / 2) - 7);

            ctx.restore();
        }
	})
})

function _FITSTRING (str, length) {
    length = length || 20;
    // Split by spaces
    var chunks = str.split(/\s+/).reduce(function (prev, curr) {
        if (prev.length && (prev[prev.length - 1] + ' ' + curr).length <= length) {
            prev[prev.length - 1] += ' ' + curr;
        } else {
            prev.push(curr);
        }
        return prev;
    }, []);
    return chunks;
}