ig.module('game.entities.projectile.player-projectile')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityPlayerProjectile = ig.Entity.extend({
        type: ig.Entity.TYPE.NONE,
        checkAgainst: ig.Entity.TYPE.BOTH,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/projectile/ship-projectile.png', 70, 70),
		itemKey: 0,
		zIndex: 200,
		size: { x: 10, y: 20 },
		offset: { x: 31, y: 26 },
		gAlpha: 0,
		targetAlpha: 1,
        alphaTime: 1,
        maxVel: { x: 1000, y: 1000 },
        explosionSound: 'explosion',
        speed: -450,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('fire', 0.25, [0]);
            // this.setScale(0.9, 0.9);
            this.currentAnim = this.anims.fire;
            if (!ig.global.wm) {
				ig.game.playerProjectile = this;
			}
            this.vel.y = this.speed;
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

        check: function (other) {
            // do nothing if other is a screen edge or a button
            if (other instanceof EntityScreenSide ||
                other instanceof EntityScreenBase ||
                other instanceof EntityButton) {
            } else {
                if (other instanceof EntityFastProjectile || other instanceof EntitySlowProjectile) {
                    // adjust position depending on explosion scale
                    ig.game.spawnEntity(EntityExplosion, this.pos.x - 28, this.pos.y - 28, {
                        explosionScale: { x: 0.2, y: 0.2 }
                    });
                    ig.soundHandler.sfxPlayer.play(this.explosionSound);
                    other.projectileCollide();
                } else if (other instanceof EntityScreenTop) {
                } else if (other instanceof EntityPlayer) {
                    return;
                } else if (other instanceof EntityUFO) {
                    other.killUfo();
                } else {
                    other.receiveDamage(1, this);
                }
                this.killProjectile();
            }
        },

        killProjectile: function () {
            ig.game.friendlyProjectile = 0;
            this.kill();
        },

        update: function () {
            this.parent();
            if (!ig.game.isPlayerAlive || ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.killProjectile();
            }
            if (ig.game.gameControl.isPaused) {
				this.vel.y = 0;
			} else {
				this.vel.y = this.speed;
			}
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
    })
})
