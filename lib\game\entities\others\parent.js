// Parent Entity
// by <PERSON><PERSON><PERSON><PERSON>
// <EMAIL>

// Notes :
// - Implement canvas on draw()
// - Use child.js to add child
// - Use maual way to change position, angle and toAngle
// - Use moveProperties() function after change position entity to move vertices and child
// - Only integrate rotation entities (parent and child)

// How to use it ?
/*
// extend on your child entity
EntityCar = EntityParent.extend({

	spriteSheet: new ig.Image('your-directory//car.png'),
	// your image is 25x50px so you draw the box around image like this
	vertices: [
		{x:0,y:0},
		{x:25,y:0},
		{x:25,y:50},
		{x:0,y:50}
	],

	update: function() {
		this.parent();
		// set car movement
		this.toAngle = 1;
		this.angle += this.toAngle;
		this.pos.x++;
		this.pos.y++;
		// move vertices and child entities to new position based on car properties (angle, position, etc)
		this.moveProperties();
	}

});
*/

ig.module('game.entities.others.parent')

.requires(
	'game.entities.others.marketjs-entity'
)

.defines(function () {
	EntityParent = EntityMarketjsEntity.extend({

		// other entity properties
		spriteSheet: null, // for draw sprite from image
		angle: 0, // angle of entity in (degree)
		toAngle: 0, // the change of angle (present angle - last) in (degree)
		vertices: [], // vertices to draw body
		childEntity: [], // variable for child entity

		init: function (x, y, settings) {
			this.parent(x, y, settings);

			// set size of car
			this.size = { x: this.spriteSheet.width, y: this.spriteSheet.height };
			// centered verticed based on spritesheet
			this.changeVerticesBasedCenter();
		},

		// add entity for child
		addChildEntity: function (childEntity) {
			childEntity.pos.x = childEntity.pos.x - this.spriteSheet.width / 2;
			childEntity.pos.y = childEntity.pos.y - this.spriteSheet.height / 2;
			this.childEntity.push(childEntity);
		},

		draw: function () {
			// draw car-body
			/// ////////////////////////////////////////////////////////////////////////
			ig.system.context.save();
			// translate based on game screen
			ig.system.context.translate(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
			ig.system.context.rotate(this.angle.toRad());
			// re-translate for canter image rotate
			ig.system.context.translate(-this.pos.x, -this.pos.y);
			ig.system.context.drawImage(
				this.spriteSheet.data,
				this.pos.x - this.spriteSheet.width / 2,
				this.pos.y - this.spriteSheet.height / 2
			);
			ig.system.context.restore();
			/// ////////////////////////////////////////////////////////////////////////

			// draw stroke for SAT collision check
			this.drawStrokeBody();
		},

		drawStrokeBody: function () {
			if (this.drawShape == true && this.vertices.length > 0) {
			// if((this.drawShape == true || (ig.game.drawShape != undefined && ig.game.drawShape == true)) && this.vertices.length > 0) {
				ig.system.context.save();
				ig.system.context.translate(-ig.game.screen.x, -ig.game.screen.y);
				ig.system.context.beginPath();
				ig.system.context.strokeStyle = 'rgba(0,255,0,1)';
				ig.system.context.moveTo(this.pos.x + this.vertices[0].x, this.pos.y + this.vertices[0].y);
				for (var i = 1; i < this.vertices.length; i++) {
					ig.system.context.lineTo(this.pos.x + this.vertices[i].x, this.pos.y + this.vertices[i].y);
				}
				ig.system.context.lineTo(this.pos.x + this.vertices[0].x, this.pos.y + this.vertices[0].y);
				ig.system.context.stroke();
				ig.system.context.restore();
			}
		},

		// use to move vertices and child entities after move position
		moveProperties: function () {
			for (var i = 0; i < this.vertices.length; i++) {
				this.vertices[i] = this.getPoint(0, 0, this.vertices[i].x, this.vertices[i].y, -this.toAngle.toRad());
			}
			for (var i = 0; i < this.childEntity.length; i++) {
				this.childEntity[i].pos = this.getPoint(0, 0, this.childEntity[i].pos.x, this.childEntity[i].pos.y, -this.toAngle.toRad());
				this.childEntity[i].angle = this.angle;
				for (var j = 0; j < this.childEntity[i].vertices.length; j++) {
					this.childEntity[i].vertices[j] = this.getPoint(0, 0, this.childEntity[i].vertices[j].x, this.childEntity[i].vertices[j].y, -this.toAngle.toRad());
				}
			}
		},

		getPoint: function (cx, cy, x, y, toAngle) {
			cos = Math.cos(toAngle),
			sin = Math.sin(toAngle),
			nx = (cos * (x - cx)) + (sin * (y - cy)) + cx,
			ny = (cos * (y - cy)) - (sin * (x - cx)) + cy;

			return { x: nx, y: ny };
		},

		changeVerticesBasedCenter: function () {
			for (var i = 0; i < this.vertices.length; i++) {
				this.vertices[i].x = this.vertices[i].x - this.spriteSheet.width / 2;
				this.vertices[i].y = this.vertices[i].y - this.spriteSheet.height / 2;
			}
		}

	});
});
