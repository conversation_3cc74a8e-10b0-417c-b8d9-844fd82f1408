ig.module('game.entities.buttons.button-more-games')
.requires(
	'game.entities.buttons.button',
	'plugins.clickable-div-layer'
)
.defines(function () {
	EntityButtonMoreGames = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		logo: new ig.AnimationSheet('media/graphics/sprites/btn_more_games.png', 248, 54),
		size: { x: 248, y: 54 },
		zIndex: 750,
		clickableLayer: null,
		link: null,
		newWindow: false,
		div_layer_name: 'more-games',
		name: 'moregames',
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.gAlpha = settings.gAlpha;

            // ig.soundHandler.unmuteAll(true);

			if (ig.global.wm) {
				return;
			}

			if (settings.div_layer_name) {
				// console.log('settings found ... using that div layer name')
				this.div_layer_name = settings.div_layer_name;
			} else {
				this.div_layer_name = 'more-games';
			}

			if (_INIT.config.enableMoreGamesButton) {
				this.anims.idle = new ig.Animation(this.logo, 0, [0], true);
				this.currentAnim = this.anims.idle;

				if (_SETTINGS.MoreGames.Link) {
					this.link = _SETTINGS.MoreGames.Link;
				}
				if (_SETTINGS.MoreGames.NewWindow) {
					this.newWindow = _SETTINGS.MoreGames.NewWindow;
				}
				// this.clickableLayer = new ClickableDivLayer(this);
			} else {
				this.kill();
			}
		},
        show: function () {
            var elem = ig.domHandler.getElementById('#' + this.div_layer_name);
            if (elem) { ig.domHandler.show(elem); }
        },
        hide: function () {
            var elem = ig.domHandler.getElementById('#' + this.div_layer_name);
            if (elem) { ig.domHandler.hide(elem); }
        },
		clicked: function () {
			this.setScale(1.1, 1.1);
		},
		clicking: function () {

		},
		released: function () {
			this.setScale(1, 1);
			try{_INIT.moreGamesButtonReleased();}
			catch(e){console.log(e);}
		},
		over: function () {
		},
		leave: function () {
			this.setScale(1, 1);
		}
	});
});
