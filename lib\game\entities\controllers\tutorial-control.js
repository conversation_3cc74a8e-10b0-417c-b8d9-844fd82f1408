ig.module('game.entities.controllers.tutorial-control')
.requires(
	'impact.entity'
)
.defines(function () {
    EntityTutorialControl = ig.Entity.extend({
        zIndex: 210,
        size: new Vector2(20, 20),
        testEnt: null,
        tween: null,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            if (!ig.global.wm) {
                // alias
                ig.game.tutorialControl = this;
            }
        },

        ready: function () {
            this.parent();
            this.initTutorial();
			this.spawnTutorialButtons();
            this.showTutorial();
        },

        initTutorial: function () {
            this.initTutorialInitialized = true;
            this.bg = ig.game.spawnEntity(EntityBg, 0, 0);
            this.panel = ig.game.spawnEntity(EntityTutorialPanel, 0, ig.system.height * 0.1875);
        },

		spawnTutorialButtons: function () {
            this.back = ig.game.spawnEntity(EntityButtonBack, ig.system.width - 75, 25, {
                control: this,
                size: new Vector2(39, 41)
            });

            if (ig.ua.mobile && ig.ua.iOS) {
                this.back.pos = {
                    x: ig.system.width - this.back.size.x * 1.75,
                    y: this.panel.pos.y + 15
                }
            }
		},

        showTutorial: function () {
            var tutorialText = _STRINGS.Tutorial;

            this.tutorialElements = {
                tutorialHeading: ig.game.spawnEntity(EntityText, Math.floor(ig.system.width * 0.5), Math.floor(ig.system.height * 0.29),
                    {
                        text: tutorialText.Heading,
                        fontSize: 72,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#49c92b'
                }),
                // tutorialText1: ig.game.spawnEntity(EntityText, Math.floor(ig.system.width * 0.5), Math.floor(ig.system.height * 0.37),
                //     {
                //         text: tutorialText.Tutorial1,
                //         fontSize: 28,
                //         fontStyle: 'eight-bit-madness',
                //         fontWeight: '100',
                //         fontColor: '#989afe'
                // }),
                // tutorialText2: ig.game.spawnEntity(EntityText, Math.floor(ig.system.width * 0.5), Math.floor(ig.system.height * 0.42),
                //     {
                //         text: tutorialText.Tutorial2,
                //         fontSize: 28,
                //         fontStyle: 'eight-bit-madness',
                //         fontWeight: '100',
                //         fontColor: '#989afe'
                // }),
                // tutorialText3: ig.game.spawnEntity(EntityText, Math.floor(ig.system.width * 0.5), Math.floor(ig.system.height * 0.47),
                //     {
                //         text: tutorialText.Tutorial3,
                //         fontSize: 28,
                //         fontStyle: 'eight-bit-madness',
                //         fontWeight: '100',
                //         fontColor: '#989afe'
                // }),
                tutorialShip: ig.game.spawnEntity(EntityTutorialShip, Math.floor(ig.system.width * 0.5) - 45, Math.floor(ig.system.height * 0.66))
            };

            if (!ig.ua.mobile) {
                this.tutorialElements.tutorialText4a = ig.game.spawnEntity(EntityText, Math.floor(ig.system.width * 0.5), Math.floor(ig.system.height * 0.36),
                {
                        text: tutorialText.Tutorial4a,
                        fontSize: 34,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '200',
                        fontColor: '#989afe',
                        underline: true
                });
                this.tutorialElements.tutorialText4b = ig.game.spawnEntity(EntityText, this.tutorialElements.tutorialText4a.pos.x, this.tutorialElements.tutorialText4a.pos.y + 35,
                {
                        text: tutorialText.Tutorial4b,
                        fontSize: 28,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#989afe'
                });
                this.tutorialElements.tutorialText5a = ig.game.spawnEntity(EntityText, this.tutorialElements.tutorialText4b.pos.x, this.tutorialElements.tutorialText4b.pos.y + 60,
                {
                        text: tutorialText.Tutorial5a,
                        fontSize: 34,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '200',
                        fontColor: '#989afe',
                        underline: true
                });
                this.tutorialElements.tutorialText5b = ig.game.spawnEntity(EntityText, this.tutorialElements.tutorialText5a.pos.x, this.tutorialElements.tutorialText5a.pos.y + 35,
                    {
                            text: tutorialText.Tutorial5b,
                            fontSize: 28,
                            fontStyle: 'eight-bit-madness',
                            fontWeight: '100',
                            fontColor: '#989afe'
                    });
                this.tutorialElements.tutorialShip.pos.y -= 1;
            } else {
                // spawn mobile left button
                this.tutorialElements.leftBtn = ig.game.spawnEntity(EntityButtonControlLeft, Math.floor(ig.system.width * 0.385), Math.floor(ig.system.height * 0.33), {
                    isTutorial: true,
                    zIndex: 210
                });
                // spawn mobile right button
                this.tutorialElements.rightBtn = ig.game.spawnEntity(EntityButtonControlRight, 0, 0, {
                    isTutorial: true,
                    zIndex: 210
                });
                // make right button's position relative to left button
                this.tutorialElements.rightBtn.pos = {
                    x: this.tutorialElements.leftBtn.pos.x,
                    y: this.tutorialElements.leftBtn.pos.y + this.tutorialElements.rightBtn.size.y + 5
                };
                // spawn mobile fire button
                this.tutorialElements.fireBtn = ig.game.spawnEntity(EntityButtonControlFire, 0, 0, {
                    isTutorial: true,
                    zIndex: 210
                });
                // make fire button's position relative to right button
                this.tutorialElements.fireBtn.pos = {
                    x: this.tutorialElements.rightBtn.pos.x - 3,
                    y: this.tutorialElements.rightBtn.pos.y + this.tutorialElements.fireBtn.size.y
                };
                // spawn instruction text for left button
                this.tutorialElements.moveLeft = ig.game.spawnEntity(EntityText,
                    Math.floor(this.tutorialElements.leftBtn.pos.x + (this.tutorialElements.leftBtn.size.x * 4)),
                    Math.floor(this.tutorialElements.leftBtn.pos.y + (this.tutorialElements.leftBtn.size.y * 0.5) + 6),
                    {
                            text: tutorialText.TutorialMobile1,
                            fontSize: 28,
                            fontStyle: 'eight-bit-madness',
                            fontWeight: '100',
                            fontColor: '#989afe'
                    }
                );
                // spawn instruction text for right button
                this.tutorialElements.moveRight = ig.game.spawnEntity(EntityText,
                    Math.floor(this.tutorialElements.rightBtn.pos.x + (this.tutorialElements.rightBtn.size.x * 4)) + 4,
                    Math.floor(this.tutorialElements.rightBtn.pos.y + (this.tutorialElements.rightBtn.size.y * 0.5) + 6),
                    {
                            text: tutorialText.TutorialMobile2,
                            fontSize: 28,
                            fontStyle: 'eight-bit-madness',
                            fontWeight: '100',
                            fontColor: '#989afe'
                    }
                );
                // spawn instruction text for fire button
                this.tutorialElements.fireA = ig.game.spawnEntity(EntityText,
                    Math.floor(this.tutorialElements.fireBtn.pos.x + (this.tutorialElements.fireBtn.size.x * 4)) - 20,
                    Math.floor(this.tutorialElements.fireBtn.pos.y + (this.tutorialElements.fireBtn.size.y * 0.5) + 6),
                    {
                            text: tutorialText.TutorialMobile3,
                            fontSize: 28,
                            fontStyle: 'eight-bit-madness',
                            fontWeight: '100',
                            fontColor: '#989afe'
                    }
                );

                // adjust position of the other entities so that the mobile tutorial fits
                // this.tutorialElements.tutorialHeading.pos.y -= 10;
                // this.tutorialElements.tutorialText1.pos.y -= 15;
                // this.tutorialElements.tutorialText2.pos.y -= 15;
                // this.tutorialElements.tutorialText3.pos.y -= 15;
                this.tutorialElements.tutorialShip.pos.y -= 25;
            }
        },

        update: function () {
            this.parent();
        },
        draw: function () {
            this.parent();
            ig.game.sortEntitiesDeferred();
        },

        removeElements: function () {
            var elements;
            for (keys in this.tutorialElements) {
                elements = this.tutorialElements[keys];
                elements.kill();
            }
            this.back.kill();
            this.bg.kill();
            this.panel.kill();
        }

    })
})
