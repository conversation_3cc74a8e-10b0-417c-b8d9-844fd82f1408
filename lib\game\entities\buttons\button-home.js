ig.module('game.entities.buttons.button-home')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonHome = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/home-button.png', 248, 54),
		size: { x: 248, y: 54 },
		zIndex: 205,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
		},
		clicked: function () {
			this.setScale(1.1, 1.1);
		},
		clicking: function () {

		},
		released: function () {
			// API_MAIN_MENU
			this.setScale(1, 1);
			ig.game.score = 0;
			ig.game.friendlyProjectile = 0;
			ig.game.enemyProjectile = 0;
			ig.game.isGame = false;
			ig.game.director.jumpTo(LevelMainMenu);
		},

		over: function () {
			// this.setScale(1.1, 1.1);
		},
		leave: function () {
			this.setScale(1, 1);
		}
	})
})
