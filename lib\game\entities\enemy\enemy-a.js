ig.module('game.entities.enemy.enemy-a')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityEnemyA = ig.Entity.extend({
		name: 'Squid',
		type: ig.Entity.TYPE.B,
		checkAgainst: ig.Entity.TYPE.A,
		collides: ig.Entity.COLLIDES.PASSIVE,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/invaders/squid.png', 70, 70),
		zIndex: 200,
		size: { x: 50, y: 50 },
		offset: { x: 10, y: 10 },
		maxVel: { x: 500, y: 500 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		deathDelay: null,
		health: 1,
		shoot: false,
		explosionSound: 'explosion',
		previousAnim: null,
		isPaused: false,
		fadeInDuration: 1,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('slow', 1, [0, 1]);
			this.addAnim('normal', 0.75, [0, 1]);
			this.addAnim('fast', 0.5, [0, 1]);
			this.addAnim('destroyed', 1, [2]);
			this.addAnim('stop', 1, [0]);
			this.setScale(0.7, 0.7);
			this.deathDelay = new ig.Timer();
			if (ig.game.gameLevelControl.enableGridBasedMovement) {
				this.movement = new GridMovement(this);
				this.movement.speed = { x: 50, y: 100 };
				this.movement.moveSize = { x: 15, y: 14 };
				this.movement.debug = false;
				this.movement.enablemoveCooldownTimer = true;
				this.movement.moveCooldown = 0.75;
			}
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

		handleMovement: function () {
			if (ig.game.gameLevelControl.enableGridBasedMovement) {
				this.movement.update();
				if (ig.game.gameLevelControl.gridForceStopMoving) {
					// Force Stop movement. (e.g. reached screen edges)
					this.movement.forceStopMoving(this.pos.x, this.pos.y);
				} else if (ig.game.gameControl.onEdge && ig.game.isPlayerAlive) {
					// move down when screen edge is reached
					this.movement.direction = GridMovement.moveType.DOWN;
					if (this.movement.reachedDestination) {
						if (this.movement.moveCooldownTimer.delta() > 0) {
							ig.game.gameControl.onEdge = false;
							ig.game.gameControl.flipDirection = !ig.game.gameControl.flipDirection;
						}
					}
				} else if (!ig.game.gameControl.onEdge && ig.game.gameControl.flipDirection && ig.game.isPlayerAlive) {
					// Move to the LEFT
					this.movement.direction = GridMovement.moveType.LEFT;
				} else if (!ig.game.gameControl.onEdge && !ig.game.gameControl.flipDirection && ig.game.isPlayerAlive) {
					// Move to the RIGHT
					this.movement.direction = GridMovement.moveType.RIGHT;
				} else if (!ig.game.isPlayerAlive) {
					// STOP moving if player dies
					this.movement.forceStopMoving(this.pos.x, this.pos.y);
				}

				// Level-based speed scaling for grid movement
				var currentLevel = ig.game.gameLevelControl.gameLevel;
				var levelSpeedMultiplier = 1 + (currentLevel - 1) * 0.15;
				var baseSpeed = 50;
				var baseCooldown = 0.75;

				// Apply level scaling
				this.movement.speed.x = Math.floor(baseSpeed * levelSpeedMultiplier);
				this.movement.moveCooldown = Math.max(0.1, baseCooldown / levelSpeedMultiplier);

				// Additional speed boost based on enemy count (existing behavior)
				if (ig.game.gameLevelControl.enemyCount >= 14 && ig.game.gameLevelControl.enemyCount <= 34) {
					// increase movement speed phase 1
					this.movement.speed.x = Math.floor(this.movement.speed.x * 1.5);
					this.movement.moveCooldown = Math.max(0.1, this.movement.moveCooldown * 0.8);
				} else if (ig.game.gameLevelControl.enemyCount > 0 && ig.game.gameLevelControl.enemyCount <= 13) {
					// increase movement speed phase 2, increase animation speed
					this.currentAnim = this.anims.fast;
					this.movement.speed.x = Math.floor(this.movement.speed.x * 2);
					this.movement.moveCooldown = Math.max(0.1, this.movement.moveCooldown * 0.5);
				}
			} else {
				// Level-based speed scaling for velocity movement
				var currentLevel = ig.game.gameLevelControl.gameLevel;
				var levelSpeedMultiplier = 1 + (currentLevel - 1) * 0.15;
				var baseSpeed = 20;
				var speed = Math.floor(baseSpeed * levelSpeedMultiplier);

				// Additional speed boost based on enemy count (existing behavior)
				if (ig.game.gameLevelControl.enemyCount >= 14 && ig.game.gameLevelControl.enemyCount <= 34) {
					// increase movement speed phase 1
					speed = Math.floor(speed * 1.5);
				} else if (ig.game.gameLevelControl.enemyCount > 0 && ig.game.gameLevelControl.enemyCount <= 13) {
					// increase movement speed phase 2, increase animation speed
					this.currentAnim = this.anims.fast;
					speed = Math.floor(speed * 2.5);
				}

				// move down if screen edge is reached
				this.vel.y = ig.game.gameControl.onEdge ? Math.floor(20 * levelSpeedMultiplier) : 0;
				// move left or right
				this.vel.x = ig.game.gameControl.onEdge ? 0 : (ig.game.gameControl.flipDirection ? -speed : speed);
			}
		},

		check: function (other) {
			if (other instanceof EntityScreenBase) {
				ig.game.gameLevelControl.hasEnemyReachedBase = true;
			}
		},

		update: function () {
			this.parent();
			// Is player alive? Is game not paused(Tutorial button clicked) ? Do the enemies haven't reached the base yet?
			if (ig.game.isPlayerAlive && !ig.game.gameControl.isPaused && !ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.currentAnim = this.previousAnim ? this.anims[this.previousAnim] : this.anims.slow;
				this.handleMovement();
				// Is the player respawned? If yes, check enemy projectile cooldown before firing
				if (ig.game.gameLevelControl.enemyProjectileCooldown.delta() > 0) {
					ig.game.gameLevelControl.fireEnemyProjectile(this.pos, this.size, this.shoot);
					this.shoot = false;
				}
			} else if (ig.game.isPlayerAlive && ig.game.gameControl.isPaused) {
				// stop movement if game is paused (e.g. tutorial button is clicked)
				this.vel = { x: 0, y: 0 };
				// this.isPaused is only a limiter to make sure this condition executes only once
				if (!this.isPaused) {
					// save previous animation before pausing
					this.previousAnim = this.currentAnim.name;
					this.isPaused = true;
				}
				this.currentAnim = this.anims.stop;
			} else if (!ig.game.isPlayerAlive) {
				// stop movement if player is killed
				this.vel = { x: 0, y: 0 };
				this.currentAnim = this.anims.stop;
			}
		},

		kill: function () {
			ig.game.spawnEntity(EntityExplosion, this.pos.x - 17, this.pos.y - 17);
			ig.soundHandler.sfxPlayer.play(this.explosionSound);
			ig.game.score += ig.game.enemyAPoints;
			ig.game.gameLevelControl.enemyCount--;
			this.currentAnim = this.anims.destroyed;
			this.parent();
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	});

	EntityEnemyA.inject(MixinEntityFade);
});
