ig.module('game.entities.title-logo')
.requires(
    'impact.entity'
)
.defines(function () {
    EntityTitleLogo = ig.Entity.extend({
        size: new Vector2(468, 181),
        animSheet: new ig.AnimationSheet('media/graphics/sprites/title.png', 468, 181),
        zIndex: 1000,
        gAlpha: 0,
        targetAlpha: 1,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
        },

        draw: function () {
            this.context = ig.system.context;
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
                this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, 0.1);
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1
		}

    })
})
