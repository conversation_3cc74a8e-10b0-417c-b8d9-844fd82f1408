ig.module('game.entities.ui.tutorial-game')
.requires(
    'impact.entity'
)
.defines(function () {
    EntityTutorialGame = ig.Entity.extend({
        checkAgainst: ig.Entity.TYPE.NONE,

        size: new Vector2(468, 181),
        animSheet: new ig.AnimationSheet('media/graphics/sprites/tutorial-panel.png', 960, 336),
        zIndex: 201,
        gAlpha: 0,
        targetAlpha: 1,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
        },

        draw: function () {
            var tutorialText = _STRINGS.Tutorial;
            this.tutorialElements = {
                tutorialHeading: ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.25,
                    {
                        text: tutorialText.Heading,
                        fontSize: 72,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#49c92b'
                }),
                tutorialText1: ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.35,
                    {
                        text: tutorialText.Tutorial1,
                        fontSize: 28,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#989afe'
                }),
                tutorialText2: ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.42,
                    {
                        text: tutorialText.Tutorial2,
                        fontSize: 28,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#989afe'
                }),
                tutorialText3: ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.49,
                    {
                        text: tutorialText.Tutorial3,
                        fontSize: 28,
                        fontStyle: 'eight-bit-madness',
                        fontWeight: '100',
                        fontColor: '#989afe'
                }),
                tutorialShip: ig.game.spawnEntity(EntityPlayer, ig.system.width * 0.5 - 45, ig.system.height * 0.62)
            };
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1
        },

        removeElements: function () {
            var elements;
            for (keys in this.tutorialElements) {
                elements = this.tutorialElements[key];
                elements.kill();
                this.parent();
            }
        }

    })
})
