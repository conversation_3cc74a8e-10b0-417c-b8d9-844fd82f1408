<!DOCTYPE html>
<html>

<head>
    <title>Space Alien Invaders</title>
    <link rel="icon" type="image/x-icon" href="media/graphics/misc/favicon.ico" />
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <meta name="viewport"
        content="width=device-width,height=device-height, initial-scale=1, maximum-scale=1, user-scalable=0, shrink-to-fit=no, minimal-ui" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <link rel="stylesheet" type="text/css" href="game.css">
    <script type="text/javascript" src="game.js"></script>

<script type="text/javascript">
        var _INIT = {
            config: {
                enableLocalStorage: false,
                timeLimit: "{{time_limit}}",
                scoreLimit: "{{score_limit}}",
                enableMoreGamesButton: true
            },
            endGame: function (score) {
                console.log("End game function triggered");
                console.log("Score", score)
            },
            playButtonReleased: function () {
                console.log("playButtonReleased");
                ig.game.isGame = true;
                ig.game.director.jumpTo(LevelGameProper);
            },
            moreGamesButtonReleased: function (score,playTime) {
                if(!$) {
                  //Wait for jQuery
                  setTimeout(function(){_INIT.endGameButtonReleased(score, playTime)}, 1);
                  return;
                }
                //Insert your function calls here
                console.log("moreGamesButtonReleased");
                console.log("Score: " + score);
                console.log("Play time: " + playTime);

                var url = document.location.href.replace("game", "gateway").replace("/index.html","");

                var gameresult = {
                  score: score,
                  playtime: playTime,
                  level: Math.floor(score / 100.0)
                };

                var exitspecs = {
                  gameclass:"", //gameclass:exitlinks[0].gateclass,
                  picklist:"",  //picklist:choiceList ,
                  score:1,  //score:scoreIndex,
                  exit:0,  //exit:winlevel, // choiceList[lastChoice]
                  result:score, //result:thisResult,
                  gameresult: gameresult
                };

                $.ajax({
                  type: "POST",
                  url: url,
                  data: exitspecs, // USE IF CHOICE RESULT
                  success: function (response){
                    window.top.location.href = response.url;
                  }, // end success
                  error: function(response){
                    try {
                      var data = JSON.parse(response.responseText);
                      if(data.url) {
                        window.location.replace(data.url);
                      }
                      else {
                        console.log("Error: ", data);
                      }
                    }
                    catch(e) {
                      console.log("Error: ", response.responseText);
                    }
                  }
                });
            },
            endGameButtonReleased:function(score,playTime){
                if(!$) {
                  //Wait for jQuery
                  setTimeout(function(){_INIT.endGameButtonReleased(score, playTime)}, 1);
                  return;
                }
                //Insert your function calls here
                console.log("endGameButtonReleased");
                console.log("Score: " + score);
                console.log("Play time: " + playTime);

                var url = document.location.href.replace("game", "gateway").replace("/index.html","");

                var gameresult = {
                  score: score,
                  playtime: playTime,
                  level: Math.floor(score / 100.0)
                };

                var exitspecs = {
                  gameclass:"", //gameclass:exitlinks[0].gateclass,
                  picklist:"",  //picklist:choiceList ,
                  score:1,  //score:scoreIndex,
                  exit:0,  //exit:winlevel, // choiceList[lastChoice]
                  result:score, //result:thisResult,
                  gameresult: gameresult
                };

                $.ajax({
                  type: "POST",
                  url: url,
                  data: exitspecs, // USE IF CHOICE RESULT
                  success: function (response){
                    window.top.location.href = response.url;
                  }, // end success
                  error: function(response){
                    try {
                      var data = JSON.parse(response.responseText);
                      if(data.url) {
                        window.location.replace(data.url);
                      }
                      else {
                        console.log("Error: ", data);
                      }
                    }
                    catch(e) {
                      console.log("Error: ", response.responseText);
                    }
                  }
                });
              }
        };
    </script>

    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/3.6.1/iframeResizer.contentWindow.min.js"></script>

</head>

<div class="gamecenter-activator"></div>

<body onload="setTimeout(function(){window.scrollTo(0,1)},1);">
    <!--body-->
    <div id="ajaxbar">
        <div id="game"><canvas id="canvas"></canvas></div>

        <div id="orientate"><img src="media/graphics/orientate/portrait.jpg" /></div>
        <div id="play" class="play" onclick=""><img src="media/graphics/splash/mobile/cover-start.jpg" /></div>
        <!--<img id="scrollDown" width="220" height="277"></img>-->

    </div>
    <!-- <div id="tempdiv"><br><br><br></div> -->
    <!-- APICode2 -->


    <!-- END OF TEST -->

</body>

</html>