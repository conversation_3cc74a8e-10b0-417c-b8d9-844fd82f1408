ig.module('game.entities.screen-side')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityScreenSide = ig.Entity.extend({
		type: ig.Entity.TYPE.A,
		checkAgainst: ig.Entity.TYPE.B,
		collides: ig.Entity.COLLIDES.FIXED,
		gravityFactor: 0,
		itemKey: 0,
		zIndex: 10000,
		size: { x: 0, y: 0 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,

		init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.size = {
                x: 1,
                y: ig.system.height
            };
		},

		draw: function () {
			this.context = ig.system.context;
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
                // this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
                this.context.globalAlpha = this.targetAlpha;
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
		},

		check: function (other) { },

		collideWith: function (other, axis) {
			// console.log('Collision!');
			if (other instanceof EntityEnemyB || other instanceof EntityEnemyA || other instanceof EntityEnemyC) {
				if (ig.game.gameLevelControl.enableGridBasedMovement) {
					if (!ig.game.gameControl.collideOnce) {
						ig.game.gameControl.edgeTimer.set(0.2);
						ig.game.gameLevelControl.gridForceStopMoving = true;
						ig.game.gameControl.collideOnce = true;
						ig.game.gameControl.onEdge = true;
					}
				} else {
					if (!ig.game.gameControl.collideOnce) {
						ig.game.gameControl.edgeTimer.set(0.7);
						ig.game.gameControl.collideOnce = true;
						ig.game.gameControl.onEdge = true;
						ig.game.gameControl.flipDirection = !ig.game.gameControl.flipDirection;
					}
				}
			} else if (other instanceof EntityUFO) {
				other.kill();
			}
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		},

		update: function () {
			this.parent();
		}
	})
})
