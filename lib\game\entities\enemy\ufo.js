ig.module('game.entities.enemy.ufo')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityUFO = ig.Entity.extend({
		name: 'UFO',
		type: ig.Entity.TYPE.B,
		collides: ig.Entity.COLLIDES.PASSIVE,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/invaders/ufo.png', 70, 70),
		zIndex: 200,
		size: { x: 60, y: 45 },
		offset: { x: 6, y: 12 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		deathDelay: null,
		deathFlag: false,
		health: 1,
		isUfoAlive: false,
		ufoSound: 'ufo',
		explosionSound: 'explosion',
		speed: 100,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('idle', 1, [0]);
			this.addAnim('destroyed', 1, [1]);
			this.setScale(0.6, 0.6);
			this.deathDelay = new ig.Timer();
			if (!ig.global.wm) {
                // alias
                ig.game.ufo = this;
			}
			ig.soundHandler.sfxPlayer.play(this.ufoSound);
			this.isUfoAlive = true;
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

		handleMovement: function () {
			if (ig.game.isPlayerAlive && !ig.game.gameControl.isPaused && !ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.vel.x = ig.game.gameLevelControl.ufoDirection == 1 ? -this.speed : this.speed;
			} else {
				this.vel.x = 0;
			}
		},

		check: function (other) {
		},

		killUfo: function () {
            ig.game.spawnEntity(EntityExplosion, this.pos.x - 17, this.pos.y - 17);
			ig.soundHandler.sfxPlayer.play(this.explosionSound);
            var points = Math.round(Math.random() * Math.floor(2));
			ig.game.score += ig.game.ufoPoints[points];
			this.currentAnim = this.anims.destroyed;
			this.kill();
		},

		kill: function () {
			ig.game.gameLevelControl.ufoTimer.reset();
			ig.soundHandler.sfxPlayer.stop(this.ufoSound);
			this.isUfoAlive = false;
			this.parent();
		},

		update: function () {
			this.parent();
			this.handleMovement();
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	})
})
