ig.module('game.entities.tutorial-ship')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityTutorialShip = ig.Entity.extend({
		type: ig.Entity.TYPE.A,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/ship.png', 70, 70),
		zIndex: 210,
		size: { x: 70, y: 70 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		isDamaged: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('idle', 0.25, [0, 1]);
			this.addAnim('destroyed', 1, [2]);
		},

		draw: function () {
			this.context = ig.system.context;
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
                // this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
                this.context.globalAlpha = this.targetAlpha;
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
		},

		kill: function () {
			this.parent();
		},

		update: function () {
			this.parent();
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	})
})
