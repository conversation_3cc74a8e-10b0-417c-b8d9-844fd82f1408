// Child Entity
// by <PERSON><PERSON><PERSON><PERSON>i
// <EMAIL>

// Notes :
// - Child of parent.js (only use with parentjs)

// How to use it ?
/*
// extend on your child entity
EntityWheel = EntityChild.extend({
	spriteSheet: new ig.Image('your-directory//wheel.png')
});

// spawn in the parent entity
ig.game.spawnEntity(EntityWheel
	positionX,
	positionY,
	{
		parentEntity: this,
		type:"front"
	}
);
*/

ig.module('game.entities.others.child')

.requires(
    'game.entities.others.marketjs-entity'
)

.defines(function () {
	EntityChild = EntityMarketjsEntity.extend({

		// other entity properties
		angle: 0, // angle of entity in (degree)
		toAngle: 0,
		alpha: 10,
		maxAlpha: 10,

		init: function (x, y, settings) {
			this.parent(x, y, settings);

			// add itself to parent
			this.parentEntity.addChildEntity(this);
		},

		draw: function () {
			// draw wheel-body
			/// ////////////////////////////////////////////////////////////////////////
			ig.system.context.save();
			// translate based on game screen
			ig.system.context.translate(this.parentEntity.pos.x + this.pos.x - ig.game.screen.x, this.parentEntity.pos.y + this.pos.y - ig.game.screen.y);
			ig.system.context.rotate(this.angle.toRad());
			// re-translate for canter image rotate
			ig.system.context.translate(-(this.parentEntity.pos.x + this.pos.x), -(this.parentEntity.pos.y + this.pos.y));
			ig.system.context.globalAlpha = this.alpha / this.maxAlpha;
			ig.system.context.drawImage(
				this.spriteSheet.data,
				this.parentEntity.pos.x + this.pos.x - this.spriteSheet.width / 2,
				this.parentEntity.pos.y + this.pos.y - this.spriteSheet.height / 2
			);
			ig.system.context.restore();
			/// ////////////////////////////////////////////////////////////////////////

			// draw stroke for SAT collision check
			this.drawStrokeBody();
		},

		drawStrokeBody: function () {
			if (this.drawShape == true && this.vertices.length > 0) {
			// if((this.drawShape == true || (ig.game.drawShape != undefined && ig.game.drawShape == true)) && this.vertices.length > 0) {
				ig.system.context.save();
				ig.system.context.translate(-ig.game.screen.x, -ig.game.screen.y);
				ig.system.context.beginPath();
				ig.system.context.strokeStyle = 'rgba(0,255,0,1)';
				ig.system.context.moveTo(this.parentEntity.pos.x + this.pos.x + this.vertices[0].x, this.parentEntity.pos.y + this.pos.y + this.vertices[0].y);
				for (var i = 1; i < this.vertices.length; i++) {
					ig.system.context.lineTo(this.parentEntity.pos.x + this.pos.x + this.vertices[i].x, this.parentEntity.pos.y + this.pos.y + this.vertices[i].y);
				}
				ig.system.context.lineTo(this.parentEntity.pos.x + this.pos.x + this.vertices[0].x, this.parentEntity.pos.y + this.pos.y + this.vertices[0].y);
				ig.system.context.stroke();
				ig.system.context.restore();
			}
		},

		// get body shape for SAT collision from vertices
		getSAT: function () {
			var tempVartices = [];
			for (var i = 0; i < this.vertices.length; i++) {
				tempVartices[i] = { x: this.parentEntity.pos.x + this.pos.x + this.vertices[i].x, y: this.parentEntity.pos.y + this.pos.y + this.vertices[i].y };
			}
			return new ig.SAT.Shape(tempVartices);
		},

		getVertices: function () {
			var tempVartices = [];
			for (var i = 0; i < this.vertices.length; i++) {
				tempVartices[i] = { x: this.parentEntity.pos.x + this.pos.x + this.vertices[i].x, y: this.parentEntity.pos.y + this.pos.y + this.vertices[i].y };
			}
			return tempVartices;
		}

	});
});
