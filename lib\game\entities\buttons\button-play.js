ig.module('game.entities.buttons.button-play')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonPlay = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/play-button.png', 248, 54),
		size: { x: 248, y: 54 },
		zIndex: 750,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
		},
		clicked: function () {
			this.setScale(1.1, 1.1);
		},
		clicking: function () {

		},
		released: function () {
			// API_START_GAME
			this.setScale(1, 1);
			try{
				_INIT.playButtonReleased();
			}catch(e){
				console.log(e);
			}
		},

		over: function () {
		},
		leave: function () {
			this.setScale(1, 1);
		}
	})
})
