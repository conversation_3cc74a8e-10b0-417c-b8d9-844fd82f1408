ig.module('game.entities.controllers.result-control')
    .requires(
        'impact.entity'
    )
    .defines(function () {
        EntityResultControl = ig.Entity.extend({
            zIndex: 10000,
            size: new Vector2(20, 20),
            testEnt: null,
            tween: null,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.global.wm) {
                    // alias
                    ig.game.resultControl = this;
                }
            },

            ready: function () {
                this.parent();
                this.initResult();
            },

            initResult: function () {
                this.initResultInitialized = true;
                this.spawnResultButtons();
            },

            spawnResultButtons: function () {

                // API_END_GAME score -> ig.game.score
                try {
                    _INIT.endGame(ig.game.score);
                } catch (e) {
                    console.log(e);
                }
                ig.game.gameLevelControl.enemyCount = 0;
                ig.game.gameLevelControl.prevGameLevel = 0;
                ig.game.gameLevelControl.gameLevel = 1;
                ig.game.gameLevelControl.hasEnemyReachedBase = false;

                var highScoreText = _STRINGS.Game.HighScore;
                var scoreText = _STRINGS.Game.Score;
                var resultText = _STRINGS.Game.GameOver;

                var score = ig.game.score;
                var highScore = ig.game.load('highScore');
                var newHighScore = ig.game.score > highScore ? ig.game.score : highScore;

                ig.game.save('highScore', newHighScore);

                this.resultButtons = {
                    home: ig.game.spawnEntity(EntityButtonHome, 0, 0, {
                        control: this
                    }),
                    restart: ig.game.spawnEntity(EntityButtonBlank, 0, 0, {
                        control: this,
                        text: _STRINGS.Game.Restart,
                        released: function () {
                            ig.game.score = 0;
                            ig.game.friendlyProjectile = 0;
                            ig.game.enemyProjectile = 0;
                            ig.game.isGame = true;
                            ig.game.director.jumpTo(LevelGameProper);
                        }
                    }),
                    endGameButton: ig.game.spawnEntity(EntityButtonBlank, 0, 0, {
                        control: this,
                        isScale: false,
                        fontSize: '27px',
                        text: _STRINGS.Game.EndGameButton,
                        released: function () {
                            var playTime = Math.round(ig.game.gameControl.gameTimer.delta());
                            try {
                                _INIT.endGameButtonReleased(ig.game.score, playTime, ig.game.sessionData.attempts, ig.game.level);
                            } catch (e) {
                                console.log(e);

                            }
                        }
                    })
                };

                this.resultButtons.home.pos = {
                    x: ig.system.width * 0.5 - (this.resultButtons.home.size.x / 2),
                    y: ig.system.height * 0.55 + (this.resultButtons.home.size.y) - 10
                }

                this.resultButtons.restart.pos = {
                    x: ig.system.width * 0.5 - (this.resultButtons.restart.size.x / 2),
                    y: this.resultButtons.home.pos.y + this.resultButtons.restart.size.y + 5
                }

                this.resultButtons.endGameButton.pos = {
                    x: ig.system.width * 0.5 - (this.resultButtons.endGameButton.size.x / 2),
                    y: this.resultButtons.restart.pos.y + this.resultButtons.endGameButton.size.y + 5
                }

                this.result = ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.34, {
                    text: resultText,
                    fontSize: 72,
                    fontStyle: 'eight-bit-madness',
                    fontWeight: '100',
                    fontColor: '#49c92b'
                });

                this.score = ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.45, {
                    text: scoreText + score,
                    fontSize: 36,
                    fontStyle: 'eight-bit-madness',
                    fontWeight: '100',
                    fontColor: '#989afe'
                });
                if(newHighScore == undefined) newHighScore = score;
                this.highScore = ig.game.spawnEntity(EntityText, ig.system.width * 0.5, ig.system.height * 0.55, {
                    text: highScoreText + newHighScore,
                    fontSize: 36,
                    fontStyle: 'eight-bit-madness',
                    fontWeight: '100',
                    fontColor: '#989afe'
                });
            },

            update: function () {
                this.parent();
            },
            draw: function () {
                this.parent();
            }

        })
    })