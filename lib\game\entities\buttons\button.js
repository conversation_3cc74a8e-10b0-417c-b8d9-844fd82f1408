ig.module('game.entities.buttons.button')
.requires(
	'impact.entity',
	'plugins.data.vector'
)
.defines(function () {
	EntityButton = ig.Entity.extend({
		collides: ig.Entity.COLLIDES.NEVER,
		type: ig.Entity.TYPE.A,
		size: new Vector2(48, 48),
		fillColor: null,
		zIndex: 95000,

		gAlpha: 0,
		targetAlpha: 1,
        alphaTime: 1,

		init: function (x, y, settings) {
			this.parent(x, y, settings);

			if (!ig.global.wm) {
				if (!isNaN(settings.zIndex)) {
					this.zIndex = settings.zIndex;
				}
			}
			// Pick a random color
			var r = Math.floor(Math.random() * 256);
			var g = Math.floor(Math.random() * 256);
			var b = Math.floor(Math.random() * 256);
			var a = 1;
			this.fillColor = 'rgba(' + r + ',' + b + ',' + g + ',' + a + ')';
		},
		clicked: function () {
			throw 'no implementation on clicked()';
		},
		clicking: function () {
			throw 'no implementation on clicking()';
		},
		released: function () {
			throw 'no implementation on released()';
		},
		draw: function () {
			this.context = ig.system.context;
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
				// // this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
                this.context.globalAlpha = this.targetAlpha;
                this.context.globalAlpha = this.targetAlpha;
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}

	});
});
