ig.module('game.entities.player')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityPlayer = ig.Entity.extend({
		type: ig.Entity.TYPE.A,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/ship.png', 70, 70),
		zIndex: 200,
		size: { x: 55, y: 70 },
		offset: { x: 8, y: 0 },
		maxVel: { x: 500, y: 500 },
		gAlpha: 0,
		targetAlpha: 0,
		alphaTime: 1,
		isDamaged: false,
		damageCount: 0,
		laserSound: 'laser',
		explosionSound: 'explosion',
		isInvincible: false,

		enableGridBasedMovement: false,
		enableContinuousFiring: true,
		continuousFiringDelay: null,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('idle', 0.25, [0, 1]);
			this.addAnim('destroyed', 1, [2]);
			if (this.enableGridBasedMovement) {
				this.movement = new GridMovement(this);
				this.movement.speed.x = 200;
				this.movement.moveSize.x = 50;
				this.movement.debug = false;
			}
			this.targetAlpha = 0;
			this.isInvincible = true;
            this.cursorRecordTimer = new ig.Timer(0.02);
            this.continuousFiringDelay = new ig.Timer(0.5);
			this.tweenInvincible();
			if (!ig.global.wm) {
				ig.game.player = this;
			}
		},

		draw: function () {
			var ctx = ig.system.context;
			ctx.save();
			ctx.globalAlpha = this.targetAlpha;
			this.parent();
			ctx.restore();
		},

		tweenInvincible: function () {
			this.tween({ targetAlpha: 1 }, 0.3, {
				easing: ig.Tween.Easing.Quadratic.EaseInOut,
				loop: ig.Tween.Loop.Reverse,
				loopCount: 6,
				onComplete: function () {
					this.isInvincible = false;
					this.damageCount = 0;
				}.bind(this)
			}).start();
		},

		fireProjectile: function () {
			if (ig.game.isPlayerAlive && !ig.game.gameControl.isPaused && ig.game.friendlyProjectile == 0) {
				ig.game.friendlyProjectile++;
				this.playerProjectile = ig.game.spawnEntity(EntityPlayerProjectile, 0, 0);
				this.playerProjectile.pos = {
					x: (this.pos.x + this.size.x / 2) - (this.playerProjectile.size.x / 2),
					y: this.pos.y
				};
				ig.soundHandler.sfxPlayer.play(this.laserSound);
				this.continuousFiringDelay.reset();
			}
		},

		damagePlayer: function () {
			// damageCount serves as a limiter to avoid multiple damage in case player takes two projectiles
			if (this.damageCount == 1 && !this.isInvincible) {
				ig.soundHandler.sfxPlayer.play(this.explosionSound);
				this.killAllProjectile();
				ig.game.gameControl.edgeTimer.pause();
				ig.game.gameLevelControl.levelTimer.pause();
				ig.game.spawnEntity(EntityExplosion, Math.floor(this.pos.x) - 10, Math.floor(this.pos.y) - 5, {
					explosionScale: { x: 0.9, y: 0.9 }
				});
				if (this.isDamaged && ig.game.gameControl.playerHealth > 0) {
					ig.game.gameControl.playerHealth--;
					ig.game.gameControl.updatePlayerLife('decrease');
					ig.game.isPlayerAlive = false;
					if (ig.game.ufo && ig.game.ufo.isUfoAlive) ig.soundHandler.sfxPlayer.stop(ig.game.ufo.ufoSound);
					ig.game.gameControl.playerDeathDelay.set(2);
					this.currentAnim = this.anims.destroyed;
					this.isDamaged = false;
				} else if (ig.game.gameControl.playerHealth <= 0) {
					this.gameOver();
				}
			}
		},

		respawnPlayer: function () {
			if (ig.game.gameControl.playerDeathDelay.delta() > 0) {
				ig.game.gameLevelControl.enemyProjectileCooldown.reset();
				ig.game.isPlayerAlive = true;
				if (ig.game.ufo && ig.game.ufo.isUfoAlive) ig.soundHandler.sfxPlayer.play(ig.game.ufo.ufoSound);
				// 4 in x-axis and 6 on y-axis is an offset so that the player will respawn on the same location
				// without these, player will respawn few pixels to the right and down
				ig.game.gameControl.spawnPlayer(this.pos.x - 4, this.pos.y - 6);
				ig.game.gameControl.edgeTimer.unpause();
				ig.game.gameLevelControl.levelTimer.unpause();
				ig.game.gameLevelControl.enemyProjectileCooldown.reset();
				this.kill();
			}
		},

		gameOver: function () {
			if (ig.game.gameControl.playerDeathDelay.delta() > 3) {
				ig.game.isPlayerAlive = false;
				ig.soundHandler.sfxPlayer.play(this.explosionSound);
				ig.game.spawnEntity(EntityExplosion, Math.floor(this.pos.x) - 10, Math.floor(this.pos.y) - 5, {
					explosionScale: { x: 0.9, y: 0.9 }
				});
				this.currentAnim = this.anims.destroyed;
				// we set a delay here so that the destroyed animation shows
				ig.game.gameControl.playerDeathDelay.set(2);
			} else if (ig.game.gameControl.playerDeathDelay.delta() > 0 && ig.game.gameControl.playerDeathDelay.delta() < 4) {
				this.kill();
			}
		},

		handlePlayerMovement: function () {
			if (this.enableGridBasedMovement) {
				this.movement.update();
				if ((ig.input.state('moveleft') || (ig.ua.mobile && (ig.game.btnCtrlLeft.click || ig.game.btnCtrlLeft.hold))) &&
						ig.game.isPlayerAlive) {
					// Move Left
					this.movement.direction = GridMovement.moveType.LEFT;
				} else if ((ig.input.state('moveright') || (ig.ua.mobile && (ig.game.btnCtrlRight.click || ig.game.btnCtrlRight.hold))) &&
							ig.game.isPlayerAlive) {
					// Move RIght
					this.movement.direction = GridMovement.moveType.RIGHT;
				} else if (!ig.game.isPlayerAlive) {
					// Stop moving if dead
					this.movement.forceStopMoving(this.pos.x, this.pos.y);
				}
			} else {
				if (!ig.ua.mobile) {
					if (ig.input.state('click') && ig.game.isPlayerAlive) {
						if (this.cursorRecordTimer && this.cursorRecordTimer.delta() > 0) {
							this.cursorRecordTimer.reset();
							this.initPos = { x: ig.game.pointer.pos.x, y: ig.game.pointer.pos.y }; // mouse position
							this.oPos = { x: this.pos.x, y: this.pos.y }; // entity position
							this.posInit = true;
						}
						if (this.posInit) {
							// calculate the distance traveled of the cursor
							var dx = ig.game.pointer.pos.x - this.initPos.x;
							if (this.pos.x >= -12 && this.pos.x <= 920) {
								// move entity depending on mouse position
								this.pos.x = this.oPos.x + dx;
							} else if (this.pos.x <= -12) {
								if (dx >= 0) {
									this.pos.x = this.oPos.x + dx;
								}
							} else if (this.pos.x >= 920) {
								if (dx <= 0) {
									this.pos.x = this.oPos.x + dx;
								}
							}
						}
					} else {
						if (ig.input.state('moveleft') && this.pos.x >= -12 && ig.game.isPlayerAlive) {
							this.vel.x = -150;
						} else if (ig.input.state('moveright') && this.pos.x <= 920 && ig.game.isPlayerAlive) {
							this.vel.x = 150;
						} else {
							this.vel.x = 0;
						}
					}
				} else {
					if ((ig.game.btnCtrlLeft.click || ig.game.btnCtrlLeft.hold) && this.pos.x >= -12 && ig.game.isPlayerAlive) {
						this.vel.x = -150;
					} else if ((ig.game.btnCtrlRight.click || ig.game.btnCtrlRight.hold) && this.pos.x <= 920 && ig.game.isPlayerAlive) {
						this.vel.x = 150;
					} else {
						this.vel.x = 0;
					}
				}
			}
		},

		check: function (other) {
		},

		kill: function () {
			this.parent();
		},

		// we kill all projectile when the player dies in order to avoid multiple death in a row
		killAllProjectile: function () {
			if (ig.game.fastProjectile) {
				ig.game.fastProjectile.killProjectile();
			}
			if (ig.game.slowProjectile) {
				ig.game.slowProjectile.killProjectile();
			}
			if (ig.game.playerProjectile) {
				ig.game.playerProjectile.killProjectile();
			}
			if (ig.game.wigglyProjectile) {
				ig.game.wigglyProjectile.killProjectile();
			}
		},

		update: function () {
			this.parent();

			if (ig.ua.mobile) {
				if (ig.game.btnCtrlFire.click && this.continuousFiringDelay && this.continuousFiringDelay.delta() > 0) { //  || ig.game.btnCtrlFire.hold
					this.fireProjectile();
				}
			} else if (ig.input.pressed('shoot') || (ig.input.pressed('click') && ig.game.gameControl.enablePlayerFiring)) {
				this.fireProjectile();
			} else if ((ig.input.state('shoot') || (ig.input.state('click') && ig.game.gameControl.enablePlayerFiring)) &&
			this.enableContinuousFiring && this.continuousFiringDelay && this.continuousFiringDelay.delta() > 0) {
				this.fireProjectile();
			}

			// if player dies, but has health remaining, then respawn the player
			if (!ig.game.isPlayerAlive && ig.game.gameControl.playerHealth > 0) {
				this.respawnPlayer();
			}

			// if any enemy reaches the base, then it's game over
			if (ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.gameOver();
			}
			var pointerEntity = ig.game.getEntitiesByType('EntityPointer');
			if (ig.input.pressed('num0')) console.log(distanceToX(pointerEntity[0].pos.x, this.pos.x + this.size.x / 2));

			this.handlePlayerMovement();
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	});
});
