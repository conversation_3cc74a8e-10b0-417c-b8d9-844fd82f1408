ig.module('game.entities.life')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityLife = ig.Entity.extend({
		type: ig.Entity.TYPE.NONE,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/life.png', 70, 70),
		itemKey: 0,
		zIndex: 200,
		size: { x: 70, y: 70 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('empty', 1, [0]);
            this.addAnim('fill', 1, [1]);
            this.setScale(0.4, 0.4);
            this.currentAnim = this.anims.empty;
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1
		}
	})
})
