ig.module('game.entities.buttons.button-control-right')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonControlRight = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/button-control-right.png', 39, 41),
		size: { x: 59, y: 61 },
		offset: { x: -10, y: -10 },
		zIndex: 205,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
		click: false,
        hold: false,
		isTutorial: false,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
			if (!this.isTutorial) {
				this.setScale(1.8, 1.8);
			} else {
				this.setScale(0.5, 0.5);
			}
			if (!ig.global.wm) {
				ig.game.btnCtrlRight = this;
			}
		},
		clicked: function () {
			if (!this.isTutorial) {
				this.setScale(1.7, 1.7);
				this.click = true;
			}
		},
		clicking: function () {
			if (!this.isTutorial) {
				this.setScale(1.7, 1.7);
				this.hold = true;
			}
		},
		released: function () {
			if (!this.isTutorial) {
				this.setScale(1.8, 1.8);
				this.click = false;
				this.hold = false;
			}
		},
		over: function () {
		},
		leave: function () {
		},
		draw: function () {
			if (!this.isTutorial) {
				if (this.click || this.hold) {
					this.currentAnim.alpha = 1;
				} else {
					this.currentAnim.alpha = 0.7;
				}
				if (!ig.game.gameControl.isPaused) {
					this.parent();
				}
			} else {
				this.parent();
			}
		},
		update: function () {
			this.parent();
			if (!this.isTutorial) {
				if (this.click || this.hold) {
					var clickingCheck = false;

					for (var t in ig.input.touches) {
						if (ig.input.touches[t].x > this.pos.x &&
							ig.input.touches[t].x < (this.pos.x + this.size.x) &&
							ig.input.touches[t].y > this.pos.y &&
							ig.input.touches[t].y < (this.pos.y + this.size.y)) {
							clickingCheck = true;
							break;
						}
					}

					if (clickingCheck == false) {
						this.released();
					}
				}
			}
		}
	})
})
