ig.module('game.entities.controllers.game-level-control')
    .requires(
        'impact.entity'
    )
    .defines(function () {
        EntityGameLevelControl = ig.Entity.extend({
            zIndex: 10000,
            size: new Vector2(0, 0),
            testEnt: null,
            tween: null,
            prevGameLevel: 0,
            gameLevel: 1,
            ufoDirection: 1,
            ufoTimer: new ig.Timer(),
            levelTimer: new ig.Timer(),
            initDelay: new ig.Timer(),
            enemyOffset: [60, 120, 180, 240, 300, 360, 420, 480, 540, 600, 660],
            enemyProjectileCooldown: null,
            enemyCount: 0,
            enableGridBasedMovement: true,
            gridForceStopMoving: false,

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.global.wm) {
                    // alias
                    ig.game.gameLevelControl = this;
                }
                this.enemyProjectileCooldown = new ig.Timer(1);
                this.row = [
                    Math.floor(ig.system.height * 0.15),
                    Math.floor(ig.system.height * 0.22),
                    Math.floor(ig.system.height * 0.29),
                    Math.floor(ig.system.height * 0.36),
                    Math.floor(ig.system.height * 0.43)
                ];
                // save row settings for use later when game level >= 10
                this.initialRow = this.row;
                // Offset this to the scaling of enemy entities to avoid decimal x-axis
                this.column = 142.5;
            },

            ready: function () {
                this.parent();
                this.spawnEnemy();
            },

            spawnEnemy: function () {
                // We have two approach, a static and a dynamic.

                // =============STATIC APPROACH===============
                // this.enemyList = [
                //     [
                //         ig.game.spawnEntity(EntityEnemyC, this.column, this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[0], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[1], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[2], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[3], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[4], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[5], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[6], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[7], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[8], this.row[0]),
                //         ig.game.spawnEntity(EntityEnemyC, this.column + this.enemyOffset[9], this.row[0])
                //     ],
                //     [
                //         ig.game.spawnEntity(EntityEnemyB, this.column, this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[0], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[1], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[2], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[3], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[4], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[5], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[6], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[7], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[8], this.row[1]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[9], this.row[1])
                //     ],
                //     [
                //         ig.game.spawnEntity(EntityEnemyB, this.column, this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[0], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[1], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[2], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[3], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[4], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[5], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[6], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[7], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[8], this.row[2]),
                //         ig.game.spawnEntity(EntityEnemyB, this.column + this.enemyOffset[9], this.row[2])
                //     ],
                //     [
                //         ig.game.spawnEntity(EntityEnemyA, this.column, this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[0], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[1], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[2], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[3], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[4], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[5], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[6], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[7], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[8], this.row[3]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[9], this.row[3])
                //     ],
                //     [
                //         ig.game.spawnEntity(EntityEnemyA, this.column, this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[0], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[1], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[2], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[3], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[4], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[5], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[6], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[7], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[8], this.row[4]),
                //         ig.game.spawnEntity(EntityEnemyA, this.column + this.enemyOffset[9], this.row[4])
                //     ]
                // ];
                // =============END STATIC APPROACH============

                // =============DYNAMIC APPROACH===============
                // We declare empty array for use later
                this.enemyList = [];
                // below is the distance between each enemy in the y-axis. We overrode the original values
                this.enemyOffset = 60;
                // below is the position of the enemy on the x-axis. We overrode the original values
                this.row = 81;
                // save row settings for use later when game level >= 10. We overrode the original values
                this.initialRow = this.row;
                // below is the enemy types. from top to bottom
                this.enemyTypes = [
                    EntityEnemyC,
                    EntityEnemyB,
                    EntityEnemyB,
                    EntityEnemyA,
                    EntityEnemyA
                ];
                // a <= this.enemyTypes.length for our rows to depend on the number of rows on the enemyTypes
                for (var a = 1; a <= this.enemyTypes.length; a++) {
                    // we push an empty array for our array to become multi-dimensional for use later
                    this.enemyList.push([]);
                    // b <= 11 because we want 11 columns of enemy
                    for (var b = 1; b <= 11; b++) {
                        if (b == 1) {
                            // first column
                            this.enemyList[a - 1].push(ig.game.spawnEntity(
                                this.enemyTypes[a - 1],
                                this.column, // x-axis
                                a == 1 ? this.row : this.row + 37 * (a - 1) // y-axis**
                            ));
                            // ** we check if this current iteration is the first row,
                            // if yes then retain original number, if no then make the number dynamic
                        } else {
                            // second to n column
                            this.enemyList[a - 1].push(ig.game.spawnEntity(
                                this.enemyTypes[a - 1],
                                this.column + this.enemyOffset * (b - 1), // x-axis
                                a == 1 ? this.row : this.row + 37 * (a - 1) // y-axis**
                            ));
                            // ** we check if this current iteration is the first row,
                            // if yes then retain original number, if no then make the number dynamic
                        }
                    }
                }
                // =============END DYNAMIC APPROACH===============
                // we count the enemies for our enemy speed modifier
                this.countEnemies();
            },

            spawnUfo: function () {
                // ufo spawns every 15 seconds
                if (this.ufoTimer.delta() > 15 && (typeof ig.game.ufo == 'undefined' || ig.game.ufo._killed)) {
                    // generate a random number. if number is > 3, then spawn a ufo.
                    var random = Math.round(Math.random() * Math.floor(10));
                    // generate a random number between 0 and 1. this determines the ufo movement direction
                    this.ufoDirection = Math.round(Math.random() * Math.floor(1));
                    if (random > 3) {
                        this.ufo = ig.game.spawnEntity(EntityUFO, 0, 0);
                        this.ufo.pos = {
                            x: this.ufoDirection == 1 ? ig.system.width - this.ufo.size.x * 1.1 : 5,
                            y: ig.system.height * 0.09
                        }
                    }
                    this.ufoTimer.reset();
                }
            },

            projectileEnemySelector: function () {
                // Is enemy projectile count less than 3?
                // Is projectile cooldown finish?
                // Is enemy initiated and is not 0?
                if (ig.game.enemyProjectile < ig.game.enemyProjectileLimit && this.enemyProjectileCooldown.delta() > 0 &&
                    typeof this.enemyList != 'undefined' && this.enemyCount != 0 && ig.game.isPlayerAlive) {
                    // filter enemy that is not killed
                    var aliveEnemy = this.enemyList.map(function (list) {
                        var arr = [];
                        list.map(function (enemy, index) {
                            if (!enemy._killed) {
                                arr.push(index);
                            }
                        });
                        return arr;
                    }).filter(function (list) {
                        return list.length;
                    });

                    // choose random row
                    var randomRow = Math.floor(Math.random() * aliveEnemy.length);
                    // choose random column
                    var randomColumn = aliveEnemy[randomRow][Math.floor(Math.random() * aliveEnemy[randomRow].length)];
                    // choose enemy based on the results of random row and column, then set shoot flag to true to fire projectile
                    this.enemyList[randomRow][randomColumn].shoot = true;
                    // start firing cooldown
                    this.enemyProjectileCooldown.reset();
                }
            },

            countEnemies: function () {
                // we use array.reduce to count the enemies using accumulator as 0,
                // then add values depending on the length of the current iteration
                // cur.length because remember we have a multi-dimensional array
                this.enemyCount = this.enemyList.reduce(function (acc, cur) {
                    return acc + cur.length;
                }, 0);
            },

            handleGameLevel: function () {
                // this condition sets the delay before spawning another set of enemies
                ig.game.level = this.gameLevel;
                if (this.enemyCount == 0 && this.levelTimer.delta() > 10) {
                    ig.game.gameControl.stepCount = 0;
                    this.prevGameLevel++;
                    // the delay before spawning new set of enemies
                    this.levelTimer.set(1);
                }

                if (this.enemyCount == 0 && this.gameLevel == this.prevGameLevel &&
                    this.gameLevel != 9 && (this.levelTimer.delta() > 0 && this.levelTimer.delta() < 1)) {
                    // make the new set of enemies spawn one step lower
                    this.row = this.row.map(function (row) {
                        row = Math.floor(row + 14);
                        return row;
                    });

                    // if player health is less than max health, add another health
                    if (ig.game.gameControl.playerHealth < ig.game.playerHealth) {
                        ig.game.gameControl.playerHealth++;
                        ig.game.gameControl.updatePlayerLife('increase');
                    }
                    this.spawnEnemy();
                    this.gameLevel++;
                    this.countEnemies();
                    // reset the movement direction of enemies
                    ig.game.gameControl.flipDirection = false;

                    // console.log('Previous level', this.prevGameLevel);
                    // console.log('Current level', this.gameLevel);
                } else if (this.enemyCount == 0 && this.gameLevel == 9 &&
                    (this.levelTimer.delta() > 0 && this.levelTimer.delta() < 1)) {
                    // if player health is < 3, add another health
                    if (ig.game.gameControl.playerHealth < ig.game.playerHealth) {
                        ig.game.gameControl.playerHealth++;
                        ig.game.gameControl.updatePlayerLife('increase');
                    }

                    // reset the game level back to 1
                    this.gameLevel = 1;
                    this.prevGameLevel = 0;
                    // reset the spawn location of enemies
                    this.row = this.initialRow;
                    this.spawnEnemy();
                    this.countEnemies();
                    // reset the movement direction of enemies
                    ig.game.gameControl.flipDirection = false;

                    // console.log('Previous level', this.prevGameLevel);
                    // console.log('Current level', this.gameLevel);
                }
            },

            // this function will make sure that the enemies distance from each other is always the same
            handleEnemyDistance: function () {
                this.enemyList.forEach(function (list, listIndex, origList) {
                    // make sure enemies are aligned vertically
                    list.forEach(function (enemy, index, list) {
                        if (index != 0 && listIndex != 0 && !ig.game.gameControl.onEdge) {
                            if (!origList[listIndex - 1][index]._killed) {
                                enemy.pos.x = origList[listIndex - 1][index].pos.x;
                            }
                        }
                    });

                    // make sure to handle only if enemies are next to each other and alive
                    var filteredList = list.filter(function (enemy) {
                        return !enemy._killed;
                    });
                    // make a correction to the distance between two enemy
                    filteredList.forEach(function (enemy, index, list) {
                        if (index != 0 && between((enemy.pos.x - list[index - 1].pos.x), 50, 70)) {
                            enemy.pos.x = list[index - 1].pos.x + 60;
                        }
                    });
                });
            },

            fireEnemyProjectile: function (pos, size, shoot) {
                var random = Math.round(Math.random() * Math.floor(3));
                if (ig.game.enemyProjectile < 3 && shoot) {
                    ig.game.enemyProjectile++;
                    // determine projectile type based on the random number result
                    switch (random) {
                        case 0:
                            var type1 = ig.game.spawnEntity(EntityFastProjectile, 0, 0);
                            type1.pos = {
                                x: (pos.x + size.x / 2) - (type1.size.x / 2),
                                y: Math.floor(pos.y + size.y + 10)
                            };
                            break;
                        case 1:
                            var type2 = ig.game.spawnEntity(EntitySlowProjectile, 0, 0);
                            type2.pos = {
                                x: (pos.x + size.x / 2) - (type2.size.x / 2),
                                y: Math.floor(pos.y + size.y + 10)
                            };
                            break;
                        case 2:
                            var type3 = ig.game.spawnEntity(EntityWigglyProjectile, 0, 0);
                            type3.pos = {
                                x: (pos.x + size.x / 2) - (type3.size.x / 2),
                                y: Math.floor(pos.y + size.y + 10)
                            };
                            break;
                            // add another case for wiggly to increase it's occurence
                        case 3:
                            var type4 = ig.game.spawnEntity(EntityWigglyProjectile, 0, 0);
                            type4.pos = {
                                x: (pos.x + size.x / 2) - (type4.size.x / 2),
                                y: Math.floor(pos.y + size.y + 10)
                            };
                            break;
                            // in case random number is not within range, choose wiggly
                        default:
                            var typeDefault = ig.game.spawnEntity(EntityWigglyProjectile, 0, 0);
                            typeDefault.pos = {
                                x: (pos.x + size.x / 2) - (typeDefault.size.x / 2),
                                y: Math.floor(pos.y + size.y + 10)
                            };
                            break;
                    }
                    shoot = false;
                }
            },

            update: function () {
                this.parent();
                this.spawnUfo();
                this.projectileEnemySelector();
                this.handleEnemyDistance();
                this.handleGameLevel();
                // if (ig.input.pressed('num0')) {
                // }
            },

            draw: function () {
                ig.game.sortEntitiesDeferred();
                this.parent();
            }

        })

        function between(x, min, max) {
            return x >= min && x <= max;
        }
    })