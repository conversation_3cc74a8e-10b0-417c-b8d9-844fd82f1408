/*
 * BASED FROM https://github.com/Joncom/impact-grid-movement
 * Original Author: <PERSON><PERSON>
 *
 * This plugin implements grid based movement.
 * Entity will always snap to position regardless of the velocity
 * Version: 1.0
 * Date Modified: 16-03-2020
 * Author: <PERSON><PERSON><PERSON> D<PERSON>
 *
 * For the guide, see comment at the end of the file.
 *
 */
ig.module('plugins.grid-movement')
.requires('impact.entity')
.defines(function () {
    GridMovement = ig.Class.extend({
        debug: false,

        direction: null,
        // moveSize is how many pixels will entity move
        moveSize: { x: 0, y: 0 },
        speed: { x: 0, y: 0 },
        entity: null,
        lastMove: null,
        destination: { x: 0, y: 0 },
        reachedDestination: true,
        cancelCurrent: false,

        inCollision: false,

        enableMoveCooldown: false,
        moveCooldownTimer: new ig.Timer(),
        moveCooldown: 0,
        isCooldownSet: false,

        canMoveFlag: true,

        init: function (entity) {
            this.entity = entity;
        },

        update: function () {
            if (this.direction !== this.lastMove && this.direction !== null) {
                this.cancelCurrent = true;
            }
            if (this.isMoving() && this.justReachedDestination()) {
                // if (ig.input.state('num1')) console.log('Stop the moving entity if at the destination.');
                this.stopMoving();
            } else if (this.isMoving() && this.justReachedDestination() && this.direction &&
                    !this.canMoveDirectionFromPosition(this.destination.x, this.destination.y, this.direction)) {
                // if (ig.input.state('num1')) console.log('Stop the moving entity when it hits a wall.');
                this.stopMoving();
            } else if (this.isMoving() && this.justReachedDestination() && this.direction &&
                    this.canMoveDirectionFromPosition(this.destination.x, this.destination.y, this.direction) &&
                    this.direction === this.lastMove && !this.cancelCurrent && this.canMoveFlag) {
                // if (ig.input.state('num1')) console.log('Destination reached, but set new destination and keep going. (Button/Command is being held)')
                this.continueMovingFromDestination();
            } else if (this.isMoving() && this.justReachedDestination() && this.direction &&
                    this.canMoveDirectionFromPosition(this.destination.x, this.destination.y, this.direction) &&
                    this.direction !== this.lastMove && !this.cancelCurrent && this.canMoveFlag) {
                // if (ig.input.state('num1')) console.log('Destination reached, but changing direction and continuing.');
                this.changeDirectionAndContinueMoving(this.direction);
            } else if (this.isMoving() && !this.justReachedDestination() && !this.cancelCurrent && this.canMoveFlag) {
                // if (ig.input.state('num1')) console.log('Destination not yet reached, so keep going');
                this.continueMovingToDestination();
            } else if (this.direction && this.canMoveDirectionFromCurrentPosition(this.direction) && this.canMoveFlag) {
                // if (ig.input.state('num1')) console.log('Not moving, but wanting to, so start!');

                if (this.enablemoveCooldownTimer) {
                    if (!this.isCooldownSet) {
                        this.setMovementCooldown();
                        this.isCooldownSet = true;
                    } else if (this.isCooldownSet && this.moveCooldownTimer.delta() > 0) {
                        // if (ig.input.state('num1')) console.log(this.entity.name, 'Moving');
                        this.startMoving(this.direction);
                    }
                } else {
                    this.startMoving(this.direction);
                }
            }

            this.canMoveFlag = true;
            this.direction = null;
            this.lastPosition = this.destination;
        },

        setMovementCooldown: function () {
            this.moveCooldownTimer.set(this.moveCooldown);
        },

        collision: function () {
            // Your logic here
        },

        getTargetPosition: function (posX, posY, direction) {
            // Set the direction, 0 if the axis is the same
            // x = 1 -> RIGHT
            // y = 1 -> DOWN
            // x = -1 -> LEFT
            // y = -1 -> UP
            var dir = { x: 0, y: 0 };

            if (direction === GridMovement.moveType.UP) dir.y = -1;
            else if (direction === GridMovement.moveType.DOWN) dir.y = 1;
            else if (direction === GridMovement.moveType.LEFT) dir.x = -1;
            else if (direction === GridMovement.moveType.RIGHT) dir.x = 1;

            var targetPosition = {
                x: posX + this.moveSize.x * dir.x,
                y: posY + this.moveSize.y * dir.y
            };
            return { x: targetPosition.x, y: targetPosition.y };
        },

        startMoving: function (direction) {
            // Get current position.
            var currPos = this.entity.pos;
            if (this.debug) console.log('Original Position: ', currPos);
            // Get new destination.
            this.reachedDestination = false;
            this.cancelCurrent = false;
            this.inCollision = false;
            this.destination = this.getTargetPosition(currPos.x, currPos.y, direction);
            if (this.debug) console.log('Target Position: ', this.destination);
            // Move the entity.
            this.setVelocityByPosition(this.destination.x, this.destination.y);
            // Remember this move direction for later use.
            this.lastMove = direction;
        },

        isMoving: function () {
            return this.reachedDestination === false;
        },

        stopMoving: function () {
            // Method only called when at destination, so snap to it now.
            this.snapToPosition(this.destination.x, this.destination.y);
            if (this.debug) console.log('Stopped At Position: ', this.entity.pos);
            // We are already at the destination.
            this.destination = { x: 0, y: 0 };
            this.reachedDestination = true;
            this.isCooldownSet = false;
            // Stop.
            this.entity.vel.x = this.entity.vel.y = 0;
        },

        forceStopMoving: function (posX, posY) {
            // Method only called when at destination, so snap to it now.
            this.snapToPosition(posX, posY);
            if (this.debug) console.log('Stopped At Position: ', this.entity.pos);
            // We are already at the destination.
            this.destination = { x: 0, y: 0 };
            this.reachedDestination = true;
            // this.isCooldownSet = false;
            this.moveCooldownTimer.set(0.1);
            // Stop.
            this.entity.vel.x = this.entity.vel.y = 0;
        },

        snapToPosition: function (x, y) {
            this.entity.pos.x = x;
            this.entity.pos.y = y;
        },

        // Do entity reached the target position?
        justReachedDestination: function () {
            var destinationX = this.destination.x;
            var destinationY = this.destination.y;
            // if (ig.input.state('num1')) console.log('destination', destinationX, destinationY, this.entity.pos, this.entity.last)
            var result = (
                (this.entity.pos.x >= destinationX && this.entity.last.x < destinationX) ||
                (this.entity.pos.x <= destinationX && this.entity.last.x > destinationX) ||
                (this.entity.pos.y >= destinationY && this.entity.last.y < destinationY) ||
                (this.entity.pos.y <= destinationY && this.entity.last.y > destinationY)
            );
            return result;
        },

        continueMovingFromDestination: function () {
            // Get new destination.
            this.destination = this.getTargetPosition(this.destination.x, this.destination.y, this.lastMove);
            // Move.
            this.setVelocityByPosition(this.destination.x, this.destination.y);
        },

        canMoveDirectionFromPosition: function (posX, posY, direction) {
            var flag = '';
            var newPos = this.getTargetPosition(posX, posY, direction);
            if ((newPos.x >= ig.system.width + this.entity.size.x || newPos.x < 0 - this.entity.size.x) && this.canMoveFlag) {
                flag = false;
                this.moveToEdgePosition(newPos);
            } else if ((newPos.y >= ig.system.height || newPos.y < 0) && this.canMoveFlag) {
                flag = false;
                this.moveToEdgePosition(newPos);
            } else {
                flag = true;
            }
            // console.log(newPos, flag)
            return flag;
        },

        changeDirectionAndContinueMoving: function (newDirection) {
            // Method only called when at destination, so snap to it now.
            this.snapToPosition(this.destination.x, this.destination.y);
            // Get new destination.
            this.destination = this.getTargetPosition(this.destination.x, this.destination.y, newDirection);
            // Move.
            this.setVelocityByPosition(this.destination.x, this.destination.y);
            // Remember this move for later.
            this.lastMove = newDirection;
        },

        continueMovingToDestination: function () {
            // Move.
            this.setVelocityByPosition(this.destination.x, this.destination.y);
        },

        canMoveDirectionFromCurrentPosition: function (direction) {
            var currPosition = this.entity.pos;
            return this.canMoveDirectionFromPosition(currPosition.x, currPosition.y, direction);
        },

        // Sets the velocity of the entity so that it will move toward the Position.
        setVelocityByPosition: function (desX, desY) {
            // Destination calculation is done in getTargetPosition
            this.entity.vel.x = this.entity.vel.y = 0;
            if (this.entity.pos.x < desX) this.entity.vel.x = this.speed.x;
            else if (this.entity.pos.x > desX) this.entity.vel.x = -this.speed.x;
            else if (this.entity.pos.y < desY) this.entity.vel.y = this.speed.y;
            else if (this.entity.pos.y > desY) this.entity.vel.y = -this.speed.y;
        }

    });

    GridMovement.moveType = {
        UP: 1,
        DOWN: 2,
        LEFT: 4,
        RIGHT: 8
    };
});

/*
 *  HOW TO USE
 *
 *  In your entity, make sure you require 'plugins.gridmovement'
 *
 *  Create instance of GridMovement(): this.movement = new GridMovement(this);
 *
 *  Add this.movement.update() to your entity update() method.
 *
 *  Add this.movement.collision() to your entity check() method to check for collisions.
 *  Update the function to suit your needs.
 *
 *  Set entity movement speed like this: this.movement.speed.x or .y
 *
 *  Set the movement direction like:
 *
 *  if (ig.input.state('left'))
 *      this.movement.direction = GridMovement.moveType.LEFT;
 *  else if (ig.input.state('right'))
 *      this.movement.direction = GridMovement.moveType.RIGHT;
 *  else if (ig.input.state('up'))
 *      this.movement.direction = GridMovement.moveType.UP;
 *  else if (ig.input.state('down'))
 *      this.movement.direction = GridMovement.moveType.DOWN;
 *
 *  To make the entity stop movement by force:
 *      this.movement.forceStopMoving();
 */
