ig.module('game.entities.screen-top')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityScreenTop = ig.Entity.extend({
		type: ig.Entity.TYPE.B,
		gravityFactor: 0,
		itemKey: 0,
		zIndex: 10000,
		size: { x: 0, y: 0 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,

		init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.size = {
                x: ig.system.width,
                y: 10
            }
		},

		draw: function () {
			this.context = ig.system.context
            if (this.gAlpha != this.targetAlpha) {
                this.context.save();
                // this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
                this.context.globalAlpha = this.targetAlpha;
                this.gAlpha = this.context.globalAlpha;
                this.parent();
                this.context.restore();
            } else {
                this.parent();
            }
        },

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1
		}
	})
})
