ig.module('game.entities.endgame-button')
.requires(
	'game.entities.game-button'
)
.defines(function() {
	EntityButtonEndGame = EntityGameButton.extend({
		buttonImg: new ig.Image('media/graphics/sprites/button_end_game.png'),
		
        init: function (x, y, settings) {
			this.parent(x, y, settings);
			
			this.pos.y = ig.system.height + this.size.y;
			this.tweenTo(x - 0.5*this.size.x, y - 0.5*this.size.y);
		},
		
		released: function() {
			this.parent();
			var playTime = Math.round(ig.game.gameControl.gameTimer.delta());
			var level = Math.floor(ig.game.playerStats.highScore / 100) + 1; 
			try{
				_INIT.endGameButtonReleased(ig.game.score , playTime, ig.game.playerStats.attempts , level);
			}catch(e){
				console.log(e);
				
				}
		}
	});
});