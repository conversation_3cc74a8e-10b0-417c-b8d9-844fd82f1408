<!DOCTYPE html>
<html>
<head>
    <title>Difficulty Scaling Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        .section { margin: 30px 0; }
    </style>
</head>
<body>
    <h1>Space Invaders - Difficulty Scaling Test</h1>
    
    <div class="section">
        <h2>Enemy Movement Speed Scaling</h2>
        <p><strong>Formula:</strong> baseSpeed * (1 + (level - 1) * 0.15)</p>
        <table>
            <tr>
                <th>Level</th>
                <th>Speed Multiplier</th>
                <th>Grid Base Speed (50)</th>
                <th>Velocity Base Speed (20)</th>
                <th>Grid Cooldown (0.75s)</th>
            </tr>
            <tbody id="enemySpeedTable"></tbody>
        </table>
    </div>

    <div class="section">
        <h2>Enemy Projectile Speed Scaling</h2>
        <p><strong>Formula:</strong> baseSpeed * (1 + (level - 1) * 0.12)</p>
        <table>
            <tr>
                <th>Level</th>
                <th>Speed Multiplier</th>
                <th>Fast Projectile (350)</th>
                <th>Slow Projectile (150)</th>
                <th>Wiggly Projectile (230)</th>
            </tr>
            <tbody id="projectileSpeedTable"></tbody>
        </table>
    </div>

    <div class="section">
        <h2>Combined Scaling Example (Level 5)</h2>
        <ul>
            <li><strong>Enemy Speed:</strong> 160% of base speed</li>
            <li><strong>Projectile Speed:</strong> 148% of base speed</li>
            <li><strong>Grid Movement:</strong> 80 pixels/frame (vs 50 base)</li>
            <li><strong>Fast Projectile:</strong> 518 pixels/frame (vs 350 base)</li>
        </ul>
    </div>

    <script>
        // Enemy movement speed scaling
        function calculateEnemySpeed(level) {
            const multiplier = 1 + (level - 1) * 0.15;
            return {
                multiplier: multiplier.toFixed(2),
                gridSpeed: Math.floor(50 * multiplier),
                velocitySpeed: Math.floor(20 * multiplier),
                cooldown: Math.max(0.1, 0.75 / multiplier).toFixed(2)
            };
        }

        // Projectile speed scaling
        function calculateProjectileSpeed(level) {
            const multiplier = 1 + (level - 1) * 0.12;
            return {
                multiplier: multiplier.toFixed(2),
                fastSpeed: Math.floor(350 * multiplier),
                slowSpeed: Math.floor(150 * multiplier),
                wigglySpeed: Math.floor(230 * multiplier)
            };
        }

        // Populate enemy speed table
        const enemyTable = document.getElementById('enemySpeedTable');
        for (let level = 1; level <= 9; level++) {
            const speeds = calculateEnemySpeed(level);
            const row = enemyTable.insertRow();
            row.innerHTML = `
                <td>${level}</td>
                <td>${speeds.multiplier}x</td>
                <td>${speeds.gridSpeed}</td>
                <td>${speeds.velocitySpeed}</td>
                <td>${speeds.cooldown}s</td>
            `;
        }

        // Populate projectile speed table
        const projectileTable = document.getElementById('projectileSpeedTable');
        for (let level = 1; level <= 9; level++) {
            const speeds = calculateProjectileSpeed(level);
            const row = projectileTable.insertRow();
            row.innerHTML = `
                <td>${level}</td>
                <td>${speeds.multiplier}x</td>
                <td>${speeds.fastSpeed}</td>
                <td>${speeds.slowSpeed}</td>
                <td>${speeds.wigglySpeed}</td>
            `;
        }
    </script>
</body>
</html>
