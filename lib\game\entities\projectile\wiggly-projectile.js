ig.module('game.entities.projectile.wiggly-projectile')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityWigglyProjectile = ig.Entity.extend({
		type: ig.Entity.TYPE.NONE,
		checkAgainst: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/projectile/wiggly-projectile.png', 70, 70),
		itemKey: 0,
		zIndex: 200,
		size: { x: 20, y: 60 },
		offset: { x: 26, y: 10 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		maxVel: { x: 1000, y: 1000 },

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('fire', 0.25, [0, 1]);
            this.setScale(0.65, 0.65);
			this.currentAnim = this.anims.fire;
			if (!ig.global.wm) {
				ig.game.wigglyProjectile = this;
			}
			this.vel.y = 230;
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

		check: function (other) {
			// do nothing if other is a screen edge or a button
			if (other instanceof EntityScreenBase ||
				other instanceof EntityButton) {
			} else {
				if (other instanceof EntityScreenBottom) {
				} else if (other instanceof EntityPlayer) {
					other.damageCount++;
					other.isDamaged = true;
					other.damagePlayer();
				} else {
					other.receiveDamage(1, this);
				}
				this.killProjectile();
			}
        },

        killProjectile: function () {
            this.kill();
            if (ig.game.enemyProjectile <= 0) {
				ig.game.enemyProjectile = 0;
			} else {
				ig.game.enemyProjectile--;
			}
		},

		update: function () {
			this.parent();
			if (!ig.game.isPlayerAlive && ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.killProjectile();
			}
			if (ig.game.gameControl.isPaused) {
				this.vel.y = 0;
			} else {
				this.vel.y = 230;
			}
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1
		}
	})
})
