ig.module('game.entities.controllers.title-control')
    .requires(
        'impact.entity'
    )
    .defines(function () {
        EntityTitleLogoControl = ig.Entity.extend({
            zIndex: 10000,
            size: new Vector2(20, 20),
            testEnt: null,
            tween: null,
            animSheetList: {
                play: new ig.AnimationSheet('media/graphics/sprites/play-button.png', 248, 54),
                fullScreenEnter: new ig.Image('media/graphics/misc/expand-button.png'),
                fullScreenExit: new ig.Image('media/graphics/misc/shrink-button.png')
            },

            init: function (x, y, settings) {
                this.parent(x, y, settings);
                if (!ig.global.wm) {
                    // alias
                    ig.game.titleControl = this;
                }

                // initialize highscore if does not exist
                if (typeof ig.game.load('highScore') == 'undefined') {
                    ig.game.save('highScore', 0);
                }
            },

            ready: function () {
                this.parent();

                // console.log('ready');
                this.initHome();
            },

            initHome: function () {
                // flag
                this.initHomeInitialized = true;
                this.title = ig.game.spawnEntity(EntityTitleLogo, 0, 0);
                this.title.pos = {
                    x: ig.system.width * 0.5 - (this.title.size.x / 2),
                    y: ig.system.height * 0.14
                }
                this.spawnHomeButtons();
            },

            spawnHomeButtons: function () {
                var highScoreText = _STRINGS.Game.HighScore;
                var highScore = ig.game.load('highScore');

                // we set highscore text position relative to title logo size and position
                this.highScore = ig.game.spawnEntity(EntityText, ig.system.width * 0.5, this.title.pos.y + this.title.size.y + 56, {
                    text: highScoreText + highScore,
                    fontSize: 36,
                    fontStyle: 'eight-bit-madness',
                    fontWeight: '100',
                    fontColor: '#989afe'
                });

                if (_INIT.config.enableTutorialButton) {

                    this.homeButtons = {

                        play: ig.game.spawnEntity(EntityButtonPlay, 0, 0, {
                            control: this,
                            size: new Vector2(248, 54)
                        }),
                        moreGames: ig.game.spawnEntity(EntityButtonMoreGames, (ig.system.width * 0.5) - (248 * 0.5), (ig.system.height * 0.675) + (54), {
                           control: this,
                           size: new Vector2(248, 54)
                        }),
                        audio: ig.game.spawnEntity(EntityButtonSound, ig.system.width - 75, 25, {
                            control: this
                        }),
                        help: ig.game.spawnEntity(EntityButtonTutorial, (ig.system.width * 0.5) - (248 * 0.5), (ig.system.height * 0.675) + (54), {
                            control: this,
                            size: new Vector2(248, 54)
                        })
                    };
                } else {
                    this.homeButtons = {

                        play: ig.game.spawnEntity(EntityButtonPlay, 0, 0, {
                            control: this,
                            size: new Vector2(248, 54)
                        }),
                        moreGames: ig.game.spawnEntity(EntityButtonMoreGames, (ig.system.width * 0.5) - (248 * 0.5), (ig.system.height * 0.675) + (54), {
                         control: this,
                        size: new Vector2(248, 54)
                        }),
                        audio: ig.game.spawnEntity(EntityButtonSound, ig.system.width - 75, 25, {
                            control: this
                        })
                    };
                }

                if (_INIT.config.enableTutorialButton) {
                    if (ig.ua.mobile && ig.ua.iOS) {
                        this.homeButtons.audio.pos = {
                            x: ig.system.width - this.homeButtons.audio.size.x * 1.75,
                            y: this.homeButtons.audio.size.y/2
                        }
                        // this.homeButtons.help.pos = {
                        //     x: this.homeButtons.audio.pos.x + 8,
                        //     y: this.homeButtons.audio.pos.y + this.homeButtons.help.size.y * 1.8
                        // }
                    }
                } else {
                    if (ig.ua.mobile && ig.ua.iOS) {
                        this.homeButtons.audio.pos = {
                            x: ig.system.width - this.homeButtons.audio.size.x * 1.75,
                            y: this.homeButtons.audio.size.y/2
                        }
                    }
                }

                // we set play button position relative to highscore text position
                this.homeButtons.play.pos = {
                    x: ig.system.width * 0.5 - this.homeButtons.play.size.x / 2,
                    y: this.highScore.pos.y + this.homeButtons.play.size.y - 10
                };
                // we set moregames button position relative to play button's position
                // this.homeButtons.moreGames.pos = {
                //     x: this.homeButtons.play.pos.x,
                //     y: this.homeButtons.play.pos.y + this.homeButtons.moreGames.size.y + 15
                // };

                this.fullscreenButton = ig.game.spawnEntity(ig.FullscreenButton, 25, 25, {
                    enterImage: this.animSheetList.fullScreenEnter,
                    exitImage: this.animSheetList.fullScreenExit
                });
            },

            update: function () {
                this.parent();
            },
            draw: function () {
                ig.game.sortEntitiesDeferred();
                this.parent();
            }

        })
    })