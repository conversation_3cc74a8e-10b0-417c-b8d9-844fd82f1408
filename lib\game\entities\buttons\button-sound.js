ig.module('game.entities.buttons.button-sound')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonSound = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/audio.png', 49, 41),
		zIndex: 205,
		size: { x: 49, y: 41 },

		mutetest: false,

		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
		buttonScale: { x: 1, y: 1 },

		name: 'soundtest',
		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.gAlpha = settings.gAlpha;
			this.addAnim('on', 1, [0]);
			this.addAnim('off', 1, [1]);
			this.setScale(this.buttonScale.x, this.buttonScale.y);
			this.readyButton = new ig.Timer(0.5);
			if (ig.game.load('sound') == 0) {
				this.currentAnim = this.anims.off;
				ig.soundHandler.muteAll(true);
			} else {
				ig.soundHandler.unmuteAll(true);
				this.currentAnim = this.anims.on;
			}
			if (!ig.global.wm) {
				ig.game.soundBtn = this;
			}
		},
        draw: function () {
            this.parent();
        },
        clicked: function () {
		},
		clicking: function () {
			if (this.readyButton.delta() > 0) {
				this.setScale(this.buttonScale.x - 0.1, this.buttonScale.y - 0.1);
			}
		},
		released: function () {
			if (this.isDisabled != true && this.readyButton.delta() > 0) {
				if (ig.game.load('sound') == 0) {
					ig.soundHandler.unmuteAll(true);
					this.currentAnim = this.anims.on;
					ig.game.save('sound', 0.5);
					ig.game.save('music', 0.5);
				} else {
					// console.log("mute")
					ig.soundHandler.muteAll(true);
					this.currentAnim = this.anims.off;
					ig.game.save('sound', 0);
					ig.game.save('music', 0);
				}
				ig.soundHandler.sfxPlayer.play('staticSound')
				this.setScale(this.buttonScale.x, this.buttonScale.y);
			}
		},

		over: function () {
			if (ig.game.gameControl) {
				ig.game.gameControl.enablePlayerFiring = false;
			}
			// this.setScale(this.buttonScale.x + 0.2, this.buttonScale.y + 0.2);
		},
		leave: function () {
			if (ig.game.gameControl) {
				ig.game.gameControl.enablePlayerFiring = true;
			}
			// this.setScale(this.buttonScale.x, this.buttonScale.y);
        }
	})
})
