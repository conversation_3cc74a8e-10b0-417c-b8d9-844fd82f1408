/*
Usage:
ig.module('game.entities.example')
.requires('impact.entity', 'plugins.entity-blinking')
.defines(function() {
    EntityExample = ig.Entity.extend({
        size: { x: 32, y: 32 },
        receiveDamage: function(amount) {
            // Blink 8 times per second for 2 seconds
            var rate = 8;
            var duration = 2;
            this.blink(rate, duration);
        },
        // ...
    });
    EntityExample.inject(MixinEntityBlinking);
});
*/
ig.module('plugins.entity-blinking')
.defines(function () {
    MixinEntityBlinking = {
        _blinkTimer: null,
        _blinkRate: null,
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this._blinkTimer = new ig.Timer();
        },
        blink: function (rate, duration) {
            this._blinkRate = rate;
            this._blinkTimer.set(duration);
        },
        draw: function () {
            var keep = this.currentAnim;
            if (this._blinkTimer.delta() < 0) {
                if (Math.floor(this._blinkTimer.delta() * this._blinkRate) % 2 === 0) {
                    this.currentAnim = undefined;
                }
            }
            this.parent();
            this.currentAnim = keep;
        }
    };
});
