ig.module('game.entities.buttons.button-tutorial-game')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonTutorialGame = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/help.png', 31, 47),
		size: { x: 31, y: 47 },
		zIndex: 205,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
		buttonScale: { x: 1, y: 1 },

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
			this.setScale(this.buttonScale.x, this.buttonScale.y);
			this.readyButton = new ig.Timer(0.5);
		},
		clicked: function () {
		},
		clicking: function () {
			if (this.readyButton.delta() > 0) {
				this.setScale(this.buttonScale.x - 0.1, this.buttonScale.y - 0.1);
			}
		},
		released: function () {
			if (this.readyButton.delta() > 0) {
				this.setScale(this.buttonScale.x, this.buttonScale.y);
				ig.game.gameControl.isPaused = true;
				ig.game.gameControl.edgeTimer.pause();
				ig.game.gameControl.playerDeathDelay.pause();
				ig.game.gameLevelControl.ufoTimer.pause();
				ig.game.gameLevelControl.levelTimer.pause();
				ig.game.gameLevelControl.enemyProjectileCooldown.pause();
				if (ig.game.ufo) { // Check if UFO is spawned
					ig.soundHandler.sfxPlayer.mute(ig.game.ufo.ufoSound);
				}
				this.tutorialControl = ig.game.spawnEntity(EntityTutorialControl, 0, 0);
				this.tutorialControl.ready();
				ig.game.gameControl.killGameButtons();
			}
		},

		over: function () {
			ig.game.gameControl.enablePlayerFiring = false;
			// this.setScale(this.buttonScale.x + 0.2, this.buttonScale.y + 0.2);
		},
		leave: function () {
			ig.game.gameControl.enablePlayerFiring = true;
			// this.setScale(this.buttonScale.x, this.buttonScale.y);
        },

        removeElements: function () {
            this.tutorialPanel.kill();
            this.tutorial.kill();
            this.parent();
        }
	})
})
