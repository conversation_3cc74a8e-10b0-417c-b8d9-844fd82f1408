ig.module('game.entities.base-shelter')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityBaseShelter = ig.Entity.extend({
		type: ig.Entity.TYPE.A,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/base-shelter.png', 70, 70),
		zIndex: 200,
		size: { x: 70, y: 50 },
		offset: { x: 0, y: 10 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		health: 0,

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('idle', 1, [0]);
			this.health = ig.game.shelterHealth;
		},

		drawHealth: function () {
			var ctx = ig.system.context;
			var xPos;
			// correct the positions of the number
			if (this.health > 19 && this.health <= 30) {
				xPos = this.size.x / 2 + this.pos.x - 7;
			} else if (this.health > 9 && this.health <= 19) {
				xPos = this.size.x / 2 + this.pos.x - 6;
			} else if (this.health <= 9) {
				xPos = this.size.x / 2 + this.pos.x - 3;
			}
			ctx.font = '100 16px eight-bit-madness';
			ctx.shadowColor = 'black';
			ctx.shadowBlur = 2;
			ctx.lineWidth = 1;
			ctx.strokeText(this.health, xPos, this.size.y / 4 + this.pos.y + 2);
			ctx.shadowBlur = 0;
			ctx.fillStyle = '#989afe';
            ctx.fillText(this.health, xPos, this.size.y / 4 + this.pos.y + 2);
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				var ctx = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					ctx.save();
					ctx.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.gAlpha = ctx.globalAlpha;
					this.parent();
					ctx.restore();
				} else {
					this.parent();
				}
				this.drawHealth();
			}
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	});
});
