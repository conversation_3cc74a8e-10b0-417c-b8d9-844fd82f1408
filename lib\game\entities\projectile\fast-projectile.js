ig.module('game.entities.projectile.fast-projectile')
.requires(
	'impact.entity'
)
.defines(function () {
	EntityFastProjectile = ig.Entity.extend({
		type: ig.Entity.TYPE.B,
		checkAgainst: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/projectile/fast-projectile.png', 70, 70),
		itemKey: 0,
		zIndex: 200,
		size: { x: 9, y: 25 },
		offset: { x: 31, y: 21 },
		gAlpha: 0,
		targetAlpha: 1,
		alphaTime: 1,
		maxVel: { x: 1500, y: 1500 },

		init: function (x, y, settings) {
			this.parent(x, y, settings);
			this.addAnim('fire', 0.25, [0]);
            // this.setScale(0.8, 0.8);
			this.currentAnim = this.anims.fire;
			if (!ig.global.wm) {
				ig.game.fastProjectile = this;
			}
			this.vel.y = 350;
		},

		draw: function () {
			if (!ig.game.gameControl.isPaused) {
				this.context = ig.system.context;
				if (this.gAlpha != this.targetAlpha) {
					this.context.save();
					// this.context.globalAlpha = this.lerp(this.gAlpha, this.targetAlpha, this.alphaTime);
					this.context.globalAlpha = this.targetAlpha;
					this.gAlpha = this.context.globalAlpha;
					this.parent();
					this.context.restore();
				} else {
					this.parent();
				}
			}
        },

		check: function (other) {
			// do nothing if other is a screen edge or a button
			if (other instanceof EntityScreenBase ||
				other instanceof EntityButton) {
			} else {
				if (other instanceof EntityScreenBottom) {
				} else if (other instanceof EntityPlayer) {
					other.damageCount++;
					other.isDamaged = true;
					other.damagePlayer();
				} else {
					other.receiveDamage(1, this);
				}
				this.killProjectile();
			}
        },

        killProjectile: function () {
			this.kill();
			ig.game.enemyProjectile--;
            if (ig.game.enemyProjectile <= 0) {
				ig.game.enemyProjectile = 0;
			}
		},

		projectileCollide: function () {
			ig.game.enemyProjectile--;
			this.kill();
		},

		update: function () {
			this.parent();
			if (!ig.game.isPlayerAlive && ig.game.gameLevelControl.hasEnemyReachedBase) {
				this.killProjectile();
			}
			if (ig.game.gameControl.isPaused) {
				this.vel.y = 0;
			} else {
				this.vel.y = 350;
			}
		},

		lerp: function (v0, v1, t) {
			return (1 - t) * v0 + t * v1;
		}
	})
})
