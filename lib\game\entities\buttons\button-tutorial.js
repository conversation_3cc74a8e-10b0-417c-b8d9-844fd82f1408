ig.module('game.entities.buttons.button-tutorial')
.requires(
	'game.entities.buttons.button'
)
.defines(function () {
	EntityButtonTutorial = EntityButton.extend({
		type: ig.Entity.TYPE.A,
		gravityFactor: 0,
		animSheet: new ig.AnimationSheet('media/graphics/sprites/btn_tutorial.png', 248, 54),
		size: { x: 248, y: 54 },
		zIndex: 750,
		gAlpha: 0,
		targetAlpha: 1,
		isDisabled: false,
		// buttonScale: { x: 1, y: 1 },

		init: function (x, y, settings) {
			this.parent(x, y, settings);
            this.addAnim('idle', 1, [0]);
			this.gAlpha = settings.gAlpha;
			// this.setScale(this.buttonScale.x, this.buttonScale.y);
		},
		clicked: function () {
			this.setScale(1.1, 1.1);
			if (!ig.game.isGame) {
				ig.game.director.jumpTo(LevelTutorialMenu);
			}
		},
		clicking: function () {

		},
		released: function () {
		},

		over: function () {
		},
		leave: function () {
			this.setScale(1, 1);
		}
	})
})
